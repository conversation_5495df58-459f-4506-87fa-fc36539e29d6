{"tenFramework": "TEN Framework", "ten": "TEN", "header": {"title": "Ten Manager", "menuApp": {"title": "アプリ", "loadApp": "アプリを読み込む", "loadAppSuccess": "アプリの読み込みに成功しました", "loadAppFailed": "アプリの読み込みに失敗しました", "reloadApp": "アプリを再読み込み", "reloadAppDescription": "アプリの再読み込みにより、現在のワークスペースが閉じられます。保存されていない変更はすべて失われます。", "reloadAppConfirmation": "アプリ {{name}} を再読み込みしてもよろしいですか？", "reloadAppSuccess": "アプリの再読み込みに成功しました", "reloadAppFailed": "アプリの再読み込みに失敗しました", "reloadAllApps": "すべてのアプリを再読み込み", "reloadAllAppsSuccess": "すべてのアプリの再読み込みに成功しました", "reloadAllAppsFailed": "すべてのアプリの再読み込みに失敗しました", "reloadAllAppsConfirmation": "すべてのアプリを再読み込みしてもよろしいですか？", "unloadApp": "アプリをアンロード", "unloadAppSuccess": "アプリのアンロードに成功しました", "unloadAppFailed": "アプリのアンロードに失敗しました", "installAll": "すべてインストール", "manageLoadedApps": "読み込まれたアプリを管理", "runApp": "アプリを実行", "openAppFolder": "アプリフォルダを開く", "createApp": "アプリを作成", "about": "アプリについて"}, "menuGraph": {"title": "グラフ", "loadGraph": "グラフを読み込む", "autoLayout": "自動レイアウト", "selectLoadedApp": "読み込まれたアプリを選択", "addNode": "ノードを追加", "addConnection": "接続を追加", "addConnectionFromNode": "{{node}}から接続を追加", "addConnectionToNode": "{{node}}に接続を追加", "about": "グラフについて"}, "menuHelp": {"title": "ヘルプ", "about": "TEN Manager Designerについて"}, "menuDesigner": {"title": "デザイナー", "about": "デザイナーについて", "preferences": "設定"}, "menuExtension": {"title": "拡張機能", "openExtensionStore": "拡張機能ストアを開く", "startRTCInteraction": "TEN AIエージェントと話す", "configTrulienceAvatar": "Trulienceアバターを設定", "about": "拡張機能について"}, "menuTenAgentTools": {"title": "TEN Agentツール"}, "language": {"title": "言語", "enUS": "English", "zhCN": "中文(简体)", "zhTW": "中文(繁體)", "jaJP": "日本語"}, "theme": {"title": "テーマ", "light": "ライト", "dark": "ダーク", "system": "システム"}, "officialSite": "公式サイト", "poweredBy": "提供:", "poweredByTenFramework": "<0></0>により提供", "github": "GitHub", "tryTENAgent": "TEN Agentを試す", "currentVersion": "現在のバージョン", "latestVersion": "最新バージョン", "currentIsLatest": "現在のバージョンが最新です", "currentIsLatestDescription": "TEN Frameworkの最新バージョンをご利用中です。\n最新バージョンの詳細は<0>TEN Framework GitHubリリースページ</0>でご確認いただけます。", "newVersionAvailable": "新しいバージョンが利用可能です", "newVersionAvailableDescription": "TEN Frameworkの新しいバージョンが利用可能です。\n新バージョンの詳細は<0>TEN Framework GitHubリリースページ</0>でご確認いただけます。"}, "action": {"edit": "編集", "delete": "削除", "pinToDock": "ドックに固定", "save": "保存", "discard": "破棄", "launchTerminal": "ターミナルを起動", "ok": "OK", "cancel": "キャンセル", "selected": "選択済み", "autoStart": "自動起動", "confirm": "確認", "upstream": "上流", "downstream": "下流", "popout": "ポップアウト", "close": "閉じる", "closeAllTabs": "すべてのタブを閉じる", "unsaved": "未保存", "launchLogViewer": "ログを表示", "stop": "停止", "search": "検索", "current": "現在", "run": "実行", "viewDetails": "詳細を表示", "reset": "リセット", "chooseBaseDir": "ベースディレクトリを選択", "deleteNodeConfirmationWithName": "ノード {{name}} を削除してもよろしいですか？", "update": "更新", "create": "作成", "deleteConnectionConfirmation": "接続を削除してもよろしいですか？", "deleteConnectionSuccess": "接続の削除に成功しました", "deleteConnectionFailed": "接続の削除に失敗しました", "replaceNode": "ノードを置き換え...", "manageApps": "アプリを管理", "runApp": "実行"}, "toast": {"saveFileSuccess": "ファイルの保存に成功しました", "saveFileFailed": "ファイルの保存に失敗しました", "fetchFileContentFailed": "ファイル内容の取得に失敗しました"}, "popup": {"editor": {"confirmSaveFile": "ファイルを保存してもよろしいですか？", "confirmSaveChanges": "変更を保存しますか？", "confirmCloseAllTabs": "すべてのタブを閉じてもよろしいですか？未保存の変更はすべて失われます。", "confirmChangePosition": "位置を変更してもよろしいですか？未保存の変更はすべて失われます。"}, "selectGraph": {"title": "アプリ/グラフを選択", "app": "アプリ", "graph": "グラフ", "select": "選択", "selected": "選択済み", "unspecified": "<未指定>", "current": "現在", "updateSuccess": "アプリ/グラフの更新に成功しました"}, "customNodeConn": {"srcTitle": "ノード[{{source}}]の接続", "connectTitle": "接続の詳細", "title": "接続", "filters": "フィルター"}, "node": {"properties": "プロパティ"}, "logViewer": {"title": "ログビューア", "appInstall": "アプリのインストール", "filteredByAddon": "アドオンでログをフィルター", "noAddons": "利用可能なアドオンがありません", "noMatchedAddons": "一致するアドオンがありません", "noLogs": "表示するログがありません", "cleanLogs": "ログをクリア"}, "default": {"defaultLabelForAppRun": "アプリ実行のデフォルトラベル", "errorFolderPathEmpty": "フォルダパスは空にできません。", "errorGetBaseDir": "ベースディレクトリの取得に失敗しました", "errorOpenAppFolder": "アプリフォルダを開くことができませんでした", "errorUnknown": "不明なエラーが発生しました。"}, "apps": {"manager": "アプリマネージャー", "tableCaption": "読み込まれたアプリの一覧", "baseDir": "ベースディレクトリ", "run": "アプリを実行", "runScript": "スクリプトを実行", "runScriptPlaceholder": "実行するスクリプトを入力してください", "create": "アプリを作成", "createAppSuccess": "アプリの作成に成功しました", "createAppFailed": "アプリの作成に失敗しました", "templateType": "テンプレートタイプ", "templateTypeDescription": "アプリのテンプレートタイプを選択してください。", "templateLanguage": "テンプレート言語", "templateLanguageDescription": "アプリのテンプレート言語を選択してください。", "templateName": "テンプレート名", "templateNameDescription": "アプリのテンプレート名を選択してください。", "templateVersion": "テンプレートバージョン", "templateVersionDescription": "アプリのテンプレートバージョンを選択してください。", "appName": "アプリ名", "appNameDescription": "アプリの名前を入力してください。", "baseDirDescription": "アプリのベースディレクトリを選択してください。", "run_opts": "オプション", "run_with_agent": "エージェントで実行"}, "graph": {"title": "ノード", "titleAddNode": "ノードを追加", "graphId": "グラフID", "nodeName": "ノード名", "addonName": "アドオン名", "searchAddon": "アドオンを検索", "noAddonFound": "アドオンが見つかりません", "useInputAddon": "'{{addonName}}' を使用", "property": "プロパティ", "addNode": "ノードを追加", "addNodeSuccess": "ノードの追加に成功しました", "addNodeError": "ノードの追加に失敗しました", "graphError": "グラフの読み込みに失敗しました", "addonError": "アドオンの読み込みに失敗しました", "deleteNodeSuccess": "ノードの削除に成功しました", "deleteNodeFailed": "ノードの削除に失敗しました", "titleAddConnection": "接続を追加", "sameNodeError": "送信元と送信先のノードは同じにできません", "srcExtension": "送信元拡張機能", "destExtension": "送信先拡張機能", "messageType": "メッセージタイプ", "messageName": "メッセージ名", "addConnection": "接続を追加", "titleUpdateNodePropertyByName": "[{{name}}] プロパティを更新", "updateNodeProperty": "プロパティを更新", "graphName": "グラフ名", "updateNodePropertySuccess": "ノードプロパティの更新に成功しました", "updateNodePropertyFailed": "ノードプロパティの更新に失敗しました", "noPropertySchema": "プロパティスキーマが見つかりません", "titleReplaceNode": "ノード[{{name}}]を置き換え...", "replaceNode": "ノードを置き換え", "replaceNodeSuccess": "ノードの置き換えに成功しました", "replaceNodeFailed": "ノードの置き換えに失敗しました"}, "doc": {"title": "{{name}}"}}, "dock": {"notSelected": "タブが選択されていません", "dockSide": "ドック位置", "left": "左", "right": "右", "bottom": "下"}, "dataTable": {"no": "No.", "name": "名前", "upstream": "上流", "downstream": "下流", "type": "タイプ", "source": "ソース", "target": "ターゲット", "actions": "アクション", "viewDetails": "詳細を表示", "delete": "削除", "openMenu": "メニューを開く", "noResults": "結果がありません。"}, "extensionStore": {"extension": "拡張機能", "title": "拡張機能ストア", "readOnly": "読み取り専用", "compatible": "互換性あり", "dependencies": "依存関係", "searchPlaceholder": "拡張機能を検索...", "installedWithSum": "{{total}}個中{{count}}個がインストール済み", "openAGraphToInstall": "拡張機能をインストールするにはグラフを開いてください", "noMatchResult": "一致する結果がありません", "matchResult": "{{count}}件の一致する結果", "install": "インストール", "installed": "インストール済み", "filter": {"showUninstalled": "未インストール", "showInstalled": "インストール済み", "sort": "並び替え", "sort-default": "デフォルト", "sort-name": "名前", "sort-name-desc": "名前（降順）", "type": "タイプ"}, "extensionTitle": "拡張機能: {{name}}", "os-arch": "OS/アーキテクチャ", "selectOsArch": "OS/アーキテクチャを選択", "os-arch-default": "デフォルト", "version": "バージョン", "hash": "ハッシュ", "versionHistory": "バージョン履歴", "selectVersion": "バージョンを選択", "versionLatest": "最新", "localAddonTip": "(ローカ<PERSON>)", "tags": "タグ"}, "rtcInteraction": {"title": "リアルタイム音声AIエージェント"}, "trulienceConfig": {"title": "Trulienceアバターの設定", "enabled": "有効", "trulienceAvatarId": "Trulience Avatar ID", "trulienceAvatarToken": "Trulience Avatar Token", "trulienceSdkUrl": "Trulience SDK URL", "trulienceAnimationURL": "Trulience Animation URL"}, "rtc": {"videoSource": {"camera": "カメラ", "screen": "画面", "cameraPlaceholder": "カメラを選択"}}, "statusBar": {"appsLoadedWithCount": "{{count}}個のアプリが読み込まれています", "appsError": "アプリの取得に失敗しました", "workspace": {"title": "現在の作業場所", "baseDir": "ベースディレクトリ", "graphName": "グラフ名"}, "feedback": {"title": "フィードバック"}}, "preferences": {"general": {"title": "一般", "language": "言語", "theme": "テーマ"}, "log": {"title": "ログ", "maxLines": "最大行数", "maxLinesDescription": "ログビューアに表示する最大行数。"}, "save": "保存", "cancel": "キャンセル", "saved": "保存済み", "savedSuccess": "設定 {{key}} の保存に成功しました"}, "components": {"combobox": {"create": "{{query}}を作成", "placeholder": "検索または新規作成", "noItems": "項目がありません", "enterValueToCreate": "新しい項目を作成するには値を入力してください"}, "messageList": {"noMessages": "メッセージがありません"}}}