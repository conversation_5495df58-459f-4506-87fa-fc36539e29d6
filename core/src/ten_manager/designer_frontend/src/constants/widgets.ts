//
// Copyright © 2025 Agora
// This file is part of TEN Framework, an open source project.
// Licensed under the Apache License, Version 2.0, with certain conditions.
// Refer to the "LICENSE" file in the root directory for more information.
//
export const CONTAINER_DEFAULT_ID = "container-default";

export const WIDGET_ID_PREFIX = "widget-";

export const EXTENSION_WIDGET_ID = WIDGET_ID_PREFIX + "extension";
export const EXTENSION_STORE_WIDGET_ID = WIDGET_ID_PREFIX + "extension-store";
export const GRAPH_SELECT_WIDGET_ID = WIDGET_ID_PREFIX + "graph-select";
export const APP_FOLDER_WIDGET_ID = WIDGET_ID_PREFIX + "app-folder";
export const APP_CREATE_WIDGET_ID = WIDGET_ID_PREFIX + "app-create";
export const APPS_MANAGER_WIDGET_ID = WIDGET_ID_PREFIX + "apps-manager";
export const APP_RUN_WIDGET_ID = WIDGET_ID_PREFIX + "app-run";
export const APPS_WIDGET_ID = WIDGET_ID_PREFIX + "apps";
export const ABOUT_WIDGET_ID = WIDGET_ID_PREFIX + "about";
export const DOC_REF_WIDGET_ID = WIDGET_ID_PREFIX + "doc-ref";
export const PREFERENCES_WIDGET_ID = WIDGET_ID_PREFIX + "preferences";

export const GRAPH_ACTIONS_WIDGET_ID = WIDGET_ID_PREFIX + "graph-actions";

export const RTC_INTERACTION_WIDGET_ID = WIDGET_ID_PREFIX + "rtc-interaction";
export const TRULIENCE_CONFIG_WIDGET_ID = WIDGET_ID_PREFIX + "trulience-config";

// --- group ids ---
// export const GROUP_DEFAULT_ID = "group-default";
export const GROUP_ABOUT_ID = "group-about";
export const GROUP_DOC_REF_ID = "group-doc-ref";
export const GROUP_EXTENSION_ID = "group-extension";
export const GROUP_GRAPH_ID = "group-graph";
export const GROUP_CUSTOM_CONNECTION_ID = "group-custom-conn";
export const GROUP_TERMINAL_ID = "group-terminal";
export const GROUP_LOG_VIEWER_ID = "group-log-viewer";
export const GROUP_EDITOR_ID = "group-editor";

export const EXTENSION_STORE_POPUP_ID = EXTENSION_STORE_WIDGET_ID + "-popup";
export const GRAPH_SELECT_POPUP_ID = GRAPH_SELECT_WIDGET_ID + "-popup";
export const APP_FOLDER_POPUP_ID = APP_FOLDER_WIDGET_ID + "-popup";
export const APP_CREATE_POPUP_ID = APP_CREATE_WIDGET_ID + "-popup";
export const APPS_MANAGER_POPUP_ID = APPS_WIDGET_ID + "-manager-popup";
export const ABOUT_POPUP_ID = ABOUT_WIDGET_ID + "-popup";
export const DOC_REF_POPUP_ID = DOC_REF_WIDGET_ID + "-popup";
export const PREFERENCES_POPUP_ID = PREFERENCES_WIDGET_ID + "-popup";
