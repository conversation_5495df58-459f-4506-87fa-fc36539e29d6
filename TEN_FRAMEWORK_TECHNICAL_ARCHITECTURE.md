# TEN Framework Technical Architecture Documentation

## Table of Contents

1. [Overview](#overview)
2. [Core Architecture Analysis](#core-architecture-analysis)
3. [Real-time Communication Deep Dive](#real-time-communication-deep-dive)
4. [Multimodal Processing Pipeline](#multimodal-processing-pipeline)
5. [Technology Stack Analysis](#technology-stack-analysis)
6. [Performance Optimizations](#performance-optimizations)
7. [Engineering Decisions](#engineering-decisions)

## Overview

The TEN Framework is a sophisticated real-time, multimodal conversational AI framework built with a C/C++ core and multi-language bindings. It employs a graph-based extension system designed for low-latency, high-throughput processing of audio, video, and data streams in real-time conversational applications.

## Core Architecture Analysis

### Graph-Based Extension System

The TEN Framework implements a **directed acyclic graph (DAG)** architecture where:

#### Node Communication Model
```c
// Core extension interface from core/src/ten_runtime/extension/extension.c
typedef struct ten_extension_t {
  ten_signature_t signature;
  ten_sanitizer_thread_check_t thread_check;
  
  // Lifecycle callbacks
  ten_extension_on_configure_func_t on_configure;
  ten_extension_on_init_func_t on_init;
  ten_extension_on_start_func_t on_start;
  ten_extension_on_stop_func_t on_stop;
  ten_extension_on_deinit_func_t on_deinit;
  
  // Message handlers
  ten_extension_on_cmd_func_t on_cmd;
  ten_extension_on_data_func_t on_data;
  ten_extension_on_audio_frame_func_t on_audio_frame;
  ten_extension_on_video_frame_func_t on_video_frame;
  
  ten_addon_host_t *addon_host;
  ten_string_t name;
  ten_env_t *ten_env;
  ten_extension_thread_t *extension_thread;
} ten_extension_t;
```

#### Message Flow Architecture
- **Commands**: Control flow messages for configuration and state management
- **Data**: Generic data payloads for text, JSON, and binary content
- **Audio Frames**: Real-time audio data with metadata (sample rate, channels, format)
- **Video Frames**: Real-time video data with pixel format, dimensions, and timestamps

### Runtime Engine Architecture

#### Core Engine Structure
```c
// From core/src/ten_runtime/engine/engine.c
typedef struct ten_engine_t {
  ten_signature_t signature;
  ten_sanitizer_thread_check_t thread_check;
  ten_ref_t ref;
  
  ten_app_t *app;
  ten_extension_context_t *extension_context;
  
  ten_runloop_t *loop;           // Event loop for message processing
  ten_event_t *runloop_is_created;
  bool is_ready_to_handle_msg;
  
  ten_hashtable_t remotes;       // Remote connections
  ten_list_t weak_remotes;
  
  ten_mutex_t *in_msgs_lock;     // Thread-safe message queue
  ten_list_t in_msgs;
  
  bool long_running_mode;
  ten_string_t graph_name;
} ten_engine_t;
```

#### Threading Model
The framework employs a **hybrid threading model**:

1. **Engine Thread**: Main event loop for message routing and graph management
2. **Extension Threads**: Dedicated threads for extension groups to prevent blocking
3. **Protocol Threads**: Separate threads for network I/O operations

```c
// Extension thread management from core/src/ten_runtime/extension_thread/extension_thread.c
typedef struct ten_extension_thread_t {
  ten_signature_t signature;
  ten_sanitizer_thread_check_t thread_check;
  
  int64_t tid;
  TEN_EXTENSION_THREAD_STATE state;
  
  ten_mutex_t *lock_mode_lock;
  bool in_lock_mode;
  
  ten_list_t extensions;              // Extensions in this thread
  ten_extension_store_t *extension_store;
  ten_extension_group_t *extension_group;
  ten_runloop_t *runloop;            // Dedicated event loop
} ten_extension_thread_t;
```

### Memory Management and Data Flow

#### Zero-Copy Buffer Management
```c
// Audio frame structure from core/include_internal/ten_runtime/msg/audio_frame/
typedef struct ten_audio_frame_t {
  ten_msg_t msg_hdr;
  ten_signature_t signature;
  
  ten_value_t sample_rate;        // int32
  ten_value_t bytes_per_sample;   // int32
  ten_value_t number_of_channels; // int32
  ten_value_t samples_per_channel;// int32
  ten_value_t timestamp;          // int64
  ten_value_t line_size;          // int32
  ten_value_t is_eof;            // bool
  ten_value_t buf;               // Zero-copy buffer
} ten_audio_frame_t;
```

#### Reference Counting System
- **Shared Pointers**: Automatic memory management for messages
- **Reference Counting**: Thread-safe reference counting for cross-thread message passing
- **Buffer Pools**: Pre-allocated buffer pools for high-frequency allocations

### Extension Lifecycle Management

#### State Machine
```
INIT → CONFIGURE → START → RUNNING → STOP → DEINIT → DESTROYED
```

#### Lifecycle Implementation
```c
// From core/src/ten_runtime/extension/extension.c
void ten_extension_on_configure(ten_extension_t *self) {
  self->state = TEN_EXTENSION_STATE_ON_CONFIGURE;
  
  if (self->on_configure) {
    int64_t begin = ten_current_time_ms();
    self->on_configure(self, self->ten_env);
    int64_t end = ten_current_time_ms();
    
    // Performance monitoring
    if (end - begin > TEN_EXTENSION_ON_XXX_WARNING_THRESHOLD_MS) {
      TEN_LOGW("[%s] on_configure() took %" PRId64 " ms", 
               ten_extension_get_name(self, true), end - begin);
    }
  }
}
```

## Real-time Communication Deep Dive

### Agora RTC Integration

#### Low-Latency Audio/Video Streaming
```typescript
// From ai_agents/playground/src/manager/rtc/rtc.ts
export class RtcManager extends AGEventEmitter<RtcEvents> {
  client: IAgoraRTCClient;
  
  constructor() {
    super();
    // VP8 codec for optimal real-time performance
    this.client = AgoraRTC.createClient({ mode: "rtc", codec: "vp8" });
  }
  
  async join({ channel, userId }: { channel: string; userId: number }) {
    const res = await apiGenAgoraData({ channel, userId });
    const { appId, token } = res.data;
    await this.client?.join(appId, channel, token, userId);
  }
}
```

#### Hardware-Level Integration
```c
// ESP32 RTC implementation from ai_agents/esp32-client/main/rtc_proc.c
int agora_rtc_proc_create(char *license, uint32_t uid) {
  rtc_service_option_t service_opt = { 0 };
  service_opt.area_code = AREA_CODE_GLOB;
  service_opt.log_cfg.log_level = RTC_LOG_WARNING;
  
  // Initialize Agora RTC SDK
  int rval = agora_rtc_init(g_app.app_id, &event_handler, &service_opt);
  
  // Create connection and join channel
  rval = agora_rtc_create_connection(&g_conn_id);
  
  rtc_channel_options_t channel_options = { 0 };
  channel_options.auto_subscribe_audio = true;
  channel_options.audio_codec_opt.audio_codec_type = AUDIO_CODEC_TYPE;
  channel_options.audio_codec_opt.pcm_sample_rate = CONFIG_PCM_SAMPLE_RATE;
  
  return agora_rtc_join_channel(g_conn_id, AI_AGENT_CHANNEL_NAME, uid, 
                               g_app.token, &channel_options);
}
```

### WebRTC Signaling and Protocol Stack

#### Protocol Layer Architecture
```c
// From core/include_internal/ten_runtime/protocol/protocol.h
typedef struct ten_protocol_t {
  ten_signature_t signature;
  ten_sanitizer_thread_check_t thread_check;
  
  ten_protocol_on_accepted_func_t on_accepted;
  ten_protocol_on_input_func_t on_input;
  ten_protocol_on_output_func_t on_output;
  ten_protocol_on_closed_func_t on_closed;
  
  ten_connection_t *attached_connection;
  ten_string_t name;
} ten_protocol_t;
```

#### Integrated vs Asynced Protocols
- **Integrated Protocols**: Share the main runloop (e.g., MessagePack)
- **Asynced Protocols**: Have dedicated threads for complex I/O operations

### Buffer Management and Frame Processing

#### Audio Frame Processing Pipeline
```c
// From core/src/ten_runtime/msg/audio_frame/pcm_frame.c
void ten_extension_on_audio_frame(ten_extension_t *self, ten_shared_ptr_t *msg) {
  if (self->on_audio_frame) {
    int64_t begin = ten_current_time_ms();
    self->on_audio_frame(self, self->ten_env, msg);
    int64_t end = ten_current_time_ms();
    
    // Latency monitoring for real-time performance
    if (end - begin > TEN_EXTENSION_ON_XXX_WARNING_THRESHOLD_MS) {
      TEN_LOGW("[%s] on_audio_frame(%s) took %" PRId64 " ms",
               ten_extension_get_name(self, true), ten_msg_get_name(msg),
               end - begin);
    }
  } else {
    // Default bypass behavior for zero-latency forwarding
    ten_env_send_audio_frame(self->ten_env, msg, NULL, NULL, NULL);
  }
}
```

#### Network Optimization Techniques

1. **Bandwidth Adaptation**:
```c
// From ai_agents/esp32-client/main/rtc_proc.h
#define BANDWIDTH_ESTIMATE_MIN_BITRATE     (500000)
#define BANDWIDTH_ESTIMATE_MAX_BITRATE     (2000000)
#define BANDWIDTH_ESTIMATE_START_BITRATE   (750000)
```

2. **Frame Buffering Strategy**:
- Adaptive jitter buffering
- Frame dropping for congestion control
- Timestamp-based synchronization

## Multimodal Processing Pipeline

### Audio Processing Chain: ASR → LLM → TTS

#### ASR Integration Example
```python
# From ai_agents/agents/ten_packages/extension/speechmatics_asr_python/extension.py
class SpeechmaticsASRExtension(AsyncExtension):
    async def on_audio_frame(self, _ten_env: AsyncTenEnv, frame: AudioFrame) -> None:
        await self.client.recv_audio_frame(frame)
    
    async def recv_audio_frame(self, frame: AudioFrame) -> None:
        frame_buf = frame.get_buf()
        self.stream_id = frame.get_property_int("stream_id")
        self.user_id = frame.get_property_string("remote_user_id")
        
        # Queue for real-time processing
        await self.audio_queue.put(frame_buf)
        if self.config.dump:
            await self.audio_dumper.push_bytes(frame_buf)
```

#### Turn Detection and Interrupt Handling
```python
# From ai_agents/agents/ten_packages/extension/ten_turn_detection/extension.py
async def _process_new_turn(self, ten_env: AsyncTenEnv, decision: TurnDetectorDecision) -> None:
    text = self.cached_text
    self.cached_text = ""
    
    self.next_turn_id += 1
    self.new_turn_started = False
    
    if decision == TurnDetectorDecision.Wait:
        ten_env.log_debug("end_of_turn by wait, no new turn to send")
        return
    
    # Send processed turn data
    out_data = Data.create("text_data")
    out_data.set_property_string("text", text)
    out_data.set_property_bool("is_final", True)
    await ten_env.send_data(data=out_data)
```

### Video Frame Processing

#### Video Frame Structure
```c
// From core/include_internal/ten_runtime/msg/video_frame/video_frame.h
typedef struct ten_video_frame_t {
  ten_msg_t msg_hdr;
  ten_signature_t signature;
  
  ten_value_t pixel_fmt;    // int32 (TEN_PIXEL_FMT)
  ten_value_t timestamp;    // int64
  ten_value_t width;        // int32
  ten_value_t height;       // int32
  ten_value_t is_eof;       // bool
  ten_value_t data;         // buf (zero-copy buffer)
} ten_video_frame_t;
```

### Data Synchronization Between Streams

#### Timeline Management
```python
# Audio timeline synchronization for multimodal processing
class AudioTimeline:
    def __init__(self):
        self.timeline = []
        self.sync_offset = 0
    
    def add_frame(self, timestamp, duration, data):
        # Maintain temporal ordering for A/V sync
        self.timeline.append({
            'timestamp': timestamp + self.sync_offset,
            'duration': duration,
            'data': data
        })
```

## Technology Stack Analysis

### Build System (GN/Ninja)

#### Dependency Management
```gn
# From BUILD.gn
group("ten_framework_all") {
  deps = [
    "//core/src/ten_runtime",
    "//core/src/ten_runtime/binding",
    "//packages/core_addon_loaders",
    "//packages/core_apps",
    "//packages/core_extensions",
    "//packages/core_protocols",
    "//third_party",
  ]
  
  if (ten_enable_ten_rust) {
    deps += [ "//core/src/ten_rust" ]
  }
  
  if (ten_enable_ten_manager) {
    deps += [ "//core/src/ten_manager" ]
  }
}
```

#### Build Optimization
```gn
# Resource pooling for build efficiency
pool("rust_action_pool") {
  depth = 1  # Limit Rust build concurrency for memory management
}

pool("serialized_action_pool") {
  depth = 1  # Real-time console output
}
```

### Inter-Process Communication

#### MessagePack Protocol Implementation
```c
// From packages/core_protocols/msgpack/protocol.c
typedef struct ten_protocol_msgpack_t {
  ten_protocol_integrated_t base;
  ten_msgpack_parser_t parser;
} ten_protocol_msgpack_t;

static void ten_protocol_msgpack_on_input(ten_protocol_msgpack_t *self,
                                          ten_buf_t input_buf,
                                          ten_list_t *result_msgs) {
  ten_msgpack_deserialize_msgs(&self->parser, input_buf, result_msgs);
}
```

#### WebSocket Integration
```c
// LibWebSockets integration for real-time communication
// From third_party/libwebsockets/include/libwebsockets/lws-service.h
LWS_VISIBLE LWS_EXTERN int
lws_service(struct lws_context *context, int timeout_ms);
```

### Concurrency and Threading

#### Extension Thread Pool
```c
// From core/src/ten_runtime/extension_thread/extension_thread.c
static void *ten_extension_thread_main(void *self_) {
  ten_extension_thread_t *self = (ten_extension_thread_t *)self_;
  return ten_extension_thread_main_actual(self);
}

void ten_extension_thread_start(ten_extension_thread_t *self) {
  ten_thread_create(ten_string_get_raw_str(&self->extension_group->name),
                    ten_extension_thread_main, self);
}
```

## Performance Optimizations

### Latency Reduction Techniques

1. **Zero-Copy Message Passing**: Direct buffer sharing between extensions
2. **Lock-Free Queues**: Atomic operations for high-frequency message passing
3. **Thread Affinity**: CPU core binding for real-time threads
4. **Memory Pool Allocation**: Pre-allocated buffers to avoid malloc/free overhead

### Resource Pooling and Management

#### Memory Management Strategy
```c
// Reference counting for automatic memory management
typedef struct ten_ref_t {
  int32_t ref_count;
  void *supervisee;
  ten_ref_on_end_of_life_func_t on_end_of_life;
} ten_ref_t;
```

### Hardware Acceleration

#### SIMD Optimizations
- Audio processing with vectorized operations
- Video frame format conversions using hardware acceleration
- Platform-specific optimizations (ARM NEON, Intel AVX)

### Scalability Considerations

#### Horizontal Scaling
- Distributed graph execution across multiple nodes
- Load balancing for extension instances
- Message routing optimization for multi-node deployments

#### Vertical Scaling
- Dynamic thread pool sizing
- Adaptive buffer management
- CPU and memory usage monitoring

## Engineering Decisions

### Real-time Conversation Capabilities

1. **Graph-Based Architecture**: Enables flexible pipeline composition while maintaining deterministic message flow
2. **Hybrid Threading Model**: Balances performance isolation with resource efficiency
3. **Zero-Copy Design**: Minimizes latency for high-frequency audio/video processing
4. **Protocol Abstraction**: Supports multiple transport mechanisms (WebRTC, WebSocket, TCP)
5. **Extension Lifecycle Management**: Provides predictable resource management and error recovery

### Design Trade-offs

- **Memory vs Latency**: Pre-allocated pools increase memory usage but reduce allocation latency
- **Flexibility vs Performance**: Graph-based routing adds overhead but enables dynamic reconfiguration
- **Thread Safety vs Speed**: Lock-free designs where possible, fine-grained locking where necessary

This architecture enables TEN Framework to achieve sub-100ms end-to-end latency for voice conversations while maintaining the flexibility to support complex multimodal AI applications.
