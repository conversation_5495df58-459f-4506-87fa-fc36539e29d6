{"_ten": {"predefined_graphs": [{"name": "va_llama4", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "<agora_token>", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": true, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY|}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION|}", "agora_asr_session_control_file_path": "session_control.conf"}}, {"type": "extension", "name": "llm", "addon": "openai_chatgpt_python", "extension_group": "chatgpt", "property": {"api_key": "${env:GROQ_CLOUD_API_KEY}", "base_url": "https://api.groq.com/openai/v1/", "frequency_penalty": 0.9, "greeting": "TEN Agent connected. How can I help you today?", "max_memory_length": 10, "max_tokens": 512, "model": "meta-llama/llama-4-scout-17b-16e-instruct", "prompt": "", "proxy_url": "${env:OPENAI_PROXY_URL}"}}, {"type": "extension", "name": "tts", "addon": "azure_tts", "extension_group": "tts", "property": {"azure_subscription_key": "${env:AZURE_TTS_KEY}", "azure_subscription_region": "${env:AZURE_TTS_REGION}", "azure_synthesis_voice_name": "en-US-AndrewMultilingualNeural"}}, {"type": "extension", "name": "interrupt_detector", "addon": "interrupt_detector_python", "extension_group": "default", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}, {"type": "extension", "name": "weatherapi_tool_python", "addon": "weatherapi_tool_python", "extension_group": "default", "property": {"api_key": "${env:WEATHERAPI_API_KEY|}"}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "llm"}]}, {"name": "on_user_left", "dest": [{"extension": "llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}]}, {"extension": "llm", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}, {"name": "tool_call", "dest": [{"extension": "weatherapi_tool_python"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}, {"name": "content_data", "dest": [{"extension": "message_collector"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "llm"}]}]}, {"extension": "weatherapi_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "llm"}]}]}]}, {"name": "qwen3", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "<agora_token>", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": true, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY|}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION|}", "agora_asr_session_control_file_path": "session_control.conf"}}, {"type": "extension", "name": "llm", "addon": "openai_chatgpt_python", "extension_group": "chatgpt", "property": {"api_key": "${env:QWEN_API_KEY}", "base_url": "https://dashscope.aliyuncs.com/compatible-mode/v1", "frequency_penalty": 0.9, "greeting": "TEN Agent connected. How can I help you today?", "max_memory_length": 10, "max_tokens": 512, "model": "qwen3-235b-a22b", "prompt": ""}}, {"type": "extension", "name": "tts", "addon": "azure_tts", "extension_group": "tts", "property": {"azure_subscription_key": "${env:AZURE_TTS_KEY}", "azure_subscription_region": "${env:AZURE_TTS_REGION}", "azure_synthesis_voice_name": "en-US-AndrewMultilingualNeural"}}, {"type": "extension", "name": "interrupt_detector", "addon": "interrupt_detector_python", "extension_group": "default", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "llm"}]}, {"name": "on_user_left", "dest": [{"extension": "llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}]}, {"extension": "llm", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}, {"name": "content_data", "dest": [{"extension": "message_collector"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "llm"}]}]}]}, {"name": "deepseek_r1", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "<agora_token>", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": true, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY|}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION|}", "agora_asr_session_control_file_path": "session_control.conf"}}, {"type": "extension", "name": "llm", "addon": "openai_chatgpt_python", "extension_group": "chatgpt", "property": {"api_key": "${env:DEEPSEEK_API_KEY}", "base_url": "https://tenagentopenai.services.ai.azure.com/models", "frequency_penalty": 0.9, "greeting": "TEN Agent connected. How can I help you today?", "max_memory_length": 10, "max_tokens": 512, "model": "DeepSeek-R1", "prompt": "", "proxy_url": "${env:OPENAI_PROXY_URL}"}}, {"type": "extension", "name": "tts", "addon": "azure_tts", "extension_group": "tts", "property": {"azure_subscription_key": "${env:AZURE_TTS_KEY}", "azure_subscription_region": "${env:AZURE_TTS_REGION}", "azure_synthesis_voice_name": "en-US-AndrewMultilingualNeural"}}, {"type": "extension", "name": "interrupt_detector", "addon": "interrupt_detector_python", "extension_group": "default", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "llm"}]}, {"name": "on_user_left", "dest": [{"extension": "llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}]}, {"extension": "llm", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}, {"name": "content_data", "dest": [{"extension": "message_collector"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "llm"}]}]}]}, {"name": "voice_assistant_realtime", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "rtc", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "subscribe_audio_sample_rate": 24000}}, {"type": "extension", "name": "v2v", "addon": "openai_v2v_python", "extension_group": "llm", "property": {"api_key": "${env:OPENAI_REALTIME_API_KEY}", "temperature": 0.9, "model": "gpt-4o-realtime-preview-2024-12-17", "max_tokens": 2048, "voice": "alloy", "language": "en-US", "server_vad": true, "dump": true, "max_history": 10}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}, {"type": "extension", "name": "weatherapi_tool_python", "addon": "weatherapi_tool_python", "extension_group": "default", "property": {"api_key": "${env:WEATHERAPI_API_KEY|}"}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "v2v"}]}, {"name": "on_user_left", "dest": [{"extension": "v2v"}]}, {"name": "on_connection_failure", "dest": [{"extension": "v2v"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "v2v"}]}]}, {"extension": "v2v", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}, {"name": "tool_call", "dest": [{"extension": "weatherapi_tool_python"}]}], "data": [{"name": "text_data", "dest": [{"extension": "message_collector"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "weatherapi_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "v2v"}]}]}]}, {"name": "va_openai_azure", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "<agora_token>", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": true, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY|}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION|}", "agora_asr_session_control_file_path": "session_control.conf", "subscribe_video_pix_fmt": 4, "subscribe_video": true}}, {"type": "extension", "name": "llm", "addon": "openai_chatgpt_python", "extension_group": "chatgpt", "property": {"api_key": "${env:OPENAI_API_KEY}", "base_url": "", "frequency_penalty": 0.9, "greeting": "TEN Agent connected. How can I help you today?", "max_memory_length": 10, "max_tokens": 512, "model": "${env:OPENAI_MODEL}", "prompt": "", "proxy_url": "${env:OPENAI_PROXY_URL}"}}, {"type": "extension", "name": "tts", "addon": "azure_tts", "extension_group": "tts", "property": {"azure_subscription_key": "${env:AZURE_TTS_KEY}", "azure_subscription_region": "${env:AZURE_TTS_REGION}", "azure_synthesis_voice_name": "en-US-AndrewMultilingualNeural"}}, {"type": "extension", "name": "interrupt_detector", "addon": "interrupt_detector_python", "extension_group": "default", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}, {"type": "extension", "name": "weatherapi_tool_python", "addon": "weatherapi_tool_python", "extension_group": "default", "property": {"api_key": "${env:WEATHERAPI_API_KEY|}"}}, {"type": "extension", "name": "vision_tool_python", "addon": "vision_tool_python", "extension_group": "default", "property": {}}, {"type": "extension", "name": "bingsearch_tool_python", "addon": "bingsearch_tool_python", "extension_group": "default", "property": {"api_key": "${env:BING_API_KEY|}"}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "llm"}]}, {"name": "on_user_left", "dest": [{"extension": "llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}], "video_frame": [{"name": "video_frame", "dest": [{"extension": "vision_tool_python"}]}]}, {"extension": "llm", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}, {"name": "tool_call", "dest": [{"extension": "weatherapi_tool_python"}, {"extension": "vision_tool_python"}, {"extension": "bingsearch_tool_python"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "llm"}]}]}, {"extension": "weatherapi_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "llm"}]}]}, {"extension": "vision_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "llm"}]}]}, {"extension": "bingsearch_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "llm"}]}]}]}, {"name": "va_openai_v2v", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "rtc", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "subscribe_audio_sample_rate": 24000}}, {"type": "extension", "name": "v2v", "addon": "openai_v2v_python", "extension_group": "llm", "property": {"api_key": "${env:OPENAI_REALTIME_API_KEY}", "temperature": 0.9, "model": "gpt-4o-realtime-preview-2024-12-17", "max_tokens": 2048, "voice": "alloy", "language": "en-US", "server_vad": true, "dump": true, "max_history": 10}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}, {"type": "extension", "name": "bingsearch_tool_python", "addon": "bingsearch_tool_python", "extension_group": "default", "property": {"api_key": "${env:BING_API_KEY|}"}}, {"type": "extension", "name": "weatherapi_tool_python", "addon": "weatherapi_tool_python", "extension_group": "default", "property": {"api_key": "${env:WEATHERAPI_API_KEY|}"}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "v2v"}]}, {"name": "on_user_left", "dest": [{"extension": "v2v"}]}, {"name": "on_connection_failure", "dest": [{"extension": "v2v"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "v2v"}]}]}, {"extension": "v2v", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}, {"name": "tool_call", "dest": [{"extension": "bingsearch_tool_python"}, {"extension": "weatherapi_tool_python"}]}], "data": [{"name": "text_data", "dest": [{"extension": "message_collector"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "bingsearch_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "v2v"}]}]}, {"extension": "weatherapi_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "v2v"}]}]}]}, {"name": "va_openai_v2v_fish", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "rtc", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "subscribe_audio_sample_rate": 24000, "enable_agora_asr": false, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION}", "agora_asr_session_control_file_path": "session_control.conf"}}, {"type": "extension", "name": "v2v", "addon": "openai_v2v_python", "extension_group": "llm", "property": {"api_key": "${env:OPENAI_REALTIME_API_KEY}", "temperature": 0.9, "model": "gpt-4o-realtime-preview-2024-12-17", "max_tokens": 2048, "audio_out": false, "input_transcript": false, "language": "en-US", "server_vad": true, "dump": true, "max_history": 10}}, {"type": "extension", "name": "tts", "addon": "fish_audio_tts", "extension_group": "tts", "property": {"api_key": "${env:FISH_AUDIO_TTS_KEY}", "base_url": "https://api.fish.audio", "model_id": "d8639b5cc95548f5afbcfe22d3ba5ce5", "optimize_streaming_latency": true, "request_timeout_seconds": 30}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}, {"type": "extension", "name": "weatherapi_tool_python", "addon": "weatherapi_tool_python", "extension_group": "tools", "property": {"api_key": "${env:WEATHERAPI_API_KEY}"}}, {"type": "extension", "name": "bingsearch_tool_python", "addon": "bingsearch_tool_python", "extension_group": "tools", "property": {"api_key": "${env:BING_API_KEY}"}}], "connections": [{"extension": "agora_rtc", "data": [{"name": "text_data", "dest": [{"extension": "message_collector"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "v2v"}]}]}, {"extension": "weatherapi_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "v2v"}]}]}, {"extension": "bingsearch_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "v2v"}]}]}, {"extension": "v2v", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}, {"name": "tool_call", "dest": [{"extension": "weatherapi_tool_python"}, {"extension": "bingsearch_tool_python"}]}, {"name": "on_user_joined", "dest": [{"extension": "v2v"}]}, {"name": "on_user_left", "dest": [{"extension": "v2v"}]}], "data": [{"name": "text_data", "dest": [{"extension": "message_collector"}, {"extension": "tts"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}]}, {"name": "va_coze_azure", "auto_start": false, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "<agora_token>", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": true, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION}", "agora_asr_session_control_file_path": "session_control.conf"}}, {"type": "extension", "name": "interrupt_detector", "addon": "interrupt_detector_python", "extension_group": "default"}, {"type": "extension", "name": "coze_python_async", "addon": "coze_python_async", "extension_group": "glue", "property": {"token": "<coze_token>", "bot_id": "<coze_bot_id>", "base_url": "https://api.coze.cn", "prompt": "", "greeting": "TEN Agent connected. How can I help you today?"}}, {"type": "extension", "name": "tts", "addon": "azure_tts", "extension_group": "tts", "property": {"azure_subscription_key": "${env:AZURE_TTS_KEY}", "azure_subscription_region": "${env:AZURE_TTS_REGION}", "azure_synthesis_voice_name": "en-US-AndrewMultilingualNeural"}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber"}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "coze_python_async"}]}, {"name": "on_user_left", "dest": [{"extension": "coze_python_async"}]}], "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "coze_python_async"}, {"extension": "message_collector"}]}]}, {"extension": "coze_python_async", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "coze_python_async"}]}]}]}, {"name": "va_gemini_v2v", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "rtc", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "subscribe_audio_sample_rate": 24000, "subscribe_video_pix_fmt": 4, "subscribe_video": true}}, {"type": "extension", "name": "v2v", "addon": "gemini_v2v_python", "extension_group": "llm", "property": {"api_key": "${env:GEMINI_API_KEY}", "dump": false, "language": "en-US", "max_tokens": 2048, "model": "gemini-2.0-flash-live-001", "server_vad": true, "temperature": 0.9, "voice": "<PERSON><PERSON>"}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}, {"type": "extension", "name": "weatherapi_tool_python", "addon": "weatherapi_tool_python", "extension_group": "default", "property": {"api_key": "${env:WEATHERAPI_API_KEY|}"}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "v2v"}]}, {"name": "on_user_left", "dest": [{"extension": "v2v"}]}, {"name": "on_connection_failure", "dest": [{"extension": "v2v"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "v2v"}]}], "video_frame": [{"name": "video_frame", "dest": [{"extension": "v2v"}]}]}, {"extension": "v2v", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}, {"name": "tool_call", "dest": [{"extension": "weatherapi_tool_python"}]}], "data": [{"name": "text_data", "dest": [{"extension": "message_collector"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "weatherapi_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "v2v"}]}]}]}, {"name": "va_gemini_v2v_native", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "rtc", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "subscribe_audio_sample_rate": 24000, "subscribe_video_pix_fmt": 4, "subscribe_video": true}}, {"type": "extension", "name": "v2v", "addon": "gemini_v2v_python", "extension_group": "llm", "property": {"api_key": "${env:GEMINI_API_KEY}", "dump": false, "language": "en-US", "max_tokens": 2048, "model": "gemini-2.5-flash-preview-native-audio-dialog", "server_vad": true, "temperature": 0.9, "voice": "<PERSON><PERSON>", "transcribe_agent": true}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}, {"type": "extension", "name": "weatherapi_tool_python", "addon": "weatherapi_tool_python", "extension_group": "default", "property": {"api_key": "${env:WEATHERAPI_API_KEY|}"}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "v2v"}]}, {"name": "on_user_left", "dest": [{"extension": "v2v"}]}, {"name": "on_connection_failure", "dest": [{"extension": "v2v"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "v2v"}]}], "video_frame": [{"name": "video_frame", "dest": [{"extension": "v2v"}]}]}, {"extension": "v2v", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}, {"name": "tool_call", "dest": [{"extension": "weatherapi_tool_python"}]}], "data": [{"name": "text_data", "dest": [{"extension": "message_collector"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "weatherapi_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "v2v"}]}]}]}, {"name": "va_azure_v2v", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "rtc", "property": {"app_id": "${env:AGORA_APP_ID}", "app_certificate": "${env:AGORA_APP_CERTIFICATE|}", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "subscribe_audio_sample_rate": 24000}}, {"type": "extension", "name": "v2v", "addon": "azure_v2v_python", "extension_group": "llm", "property": {"max_tokens": 2048, "base_uri": "${env:AZURE_AI_FOUNDRY_BASE_URI}", "api_key": "${env:AZURE_AI_FOUNDRY_API_KEY}", "temperature": 0.9, "server_vad": true, "language": "en-US", "model": "gpt-4o", "enable_storage": false}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}, {"type": "extension", "name": "weatherapi_tool_python", "addon": "weatherapi_tool_python", "extension_group": "default", "property": {"api_key": "${env:WEATHERAPI_API_KEY|}"}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "v2v"}]}, {"name": "on_user_left", "dest": [{"extension": "v2v"}]}, {"name": "on_connection_failure", "dest": [{"extension": "v2v"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "v2v"}]}], "video_frame": [{"name": "video_frame", "dest": [{"extension": "v2v"}]}]}, {"extension": "v2v", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}, {"name": "tool_call", "dest": [{"extension": "weatherapi_tool_python"}]}], "data": [{"name": "text_data", "dest": [{"extension": "message_collector"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "weatherapi_tool_python", "cmd": [{"name": "tool_register", "dest": [{"extension": "v2v"}]}]}]}, {"name": "va_dify_azure", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "<agora_token>", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": true, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY|}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION|}", "agora_asr_session_control_file_path": "session_control.conf"}}, {"type": "extension", "name": "llm", "addon": "dify_python", "extension_group": "chatgpt", "property": {"api_key": "${env:DIFY_API_KEY}", "base_url": "https://api.dify.ai/v1", "greeting": "TEN Agent connected with <PERSON><PERSON>. How can I help you today?", "user_id": "User"}}, {"type": "extension", "name": "tts", "addon": "azure_tts", "extension_group": "tts", "property": {"azure_subscription_key": "${env:AZURE_TTS_KEY}", "azure_subscription_region": "${env:AZURE_TTS_REGION}", "azure_synthesis_voice_name": "en-US-AndrewMultilingualNeural"}}, {"type": "extension", "name": "interrupt_detector", "addon": "interrupt_detector_python", "extension_group": "default", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "llm"}]}, {"name": "on_user_left", "dest": [{"extension": "llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}]}, {"extension": "llm", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "llm"}]}]}]}, {"name": "story_teller_stt_integrated", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "<agora_token>", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": true, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY|}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION|}", "agora_asr_session_control_file_path": "session_control.conf"}}, {"type": "extension", "name": "llm", "addon": "openai_chatgpt_python", "extension_group": "chatgpt", "property": {"api_key": "${env:OPENAI_API_KEY}", "base_url": "", "frequency_penalty": 0.9, "greeting": "TEN Agent connected. How can I help you today?", "max_memory_length": 10, "max_tokens": 512, "model": "${env:OPENAI_MODEL}", "prompt": "You are an ai agent bot producing child picture books. Each response should be short and no more than 50 words as it's for child. \nFor every response relevant to the story-telling, you will use the 'image_generate' tool to create an image based on the description or key moment in that part of the story. \n The story should be set in a fantasy world. Try asking questions relevant to the story to decide how the story should proceed. Every response should include rich, vivid descriptions that will guide the 'image_generate' tool to produce an image that aligns with the scene or mood.\n Whether it’s the setting, a character’s expression, or a dramatic moment, the paragraph should give enough detail for a meaningful visual representation.", "proxy_url": "${env:OPENAI_PROXY_URL}"}}, {"type": "extension", "name": "tts", "addon": "azure_tts", "extension_group": "tts", "property": {"azure_subscription_key": "${env:AZURE_TTS_KEY}", "azure_subscription_region": "${env:AZURE_TTS_REGION}", "azure_synthesis_voice_name": "en-US-AndrewMultilingualNeural"}}, {"type": "extension", "name": "interrupt_detector", "addon": "interrupt_detector_python", "extension_group": "default", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}, {"type": "extension", "name": "openai_image_generate_tool", "addon": "openai_image_generate_tool", "extension_group": "default", "property": {"api_key": "${env:OPENAI_API_KEY}"}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "llm"}]}, {"name": "on_user_left", "dest": [{"extension": "llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}]}, {"extension": "llm", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}, {"name": "tool_call", "dest": [{"extension": "openai_image_generate_tool"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "llm"}]}]}, {"extension": "openai_image_generate_tool", "cmd": [{"name": "tool_register", "dest": [{"extension": "llm"}]}], "data": [{"name": "content_data", "dest": [{"extension": "message_collector"}]}]}]}, {"name": "va_nova_multimodal_aws", "auto_start": true, "nodes": [{"type": "extension", "name": "agora_rtc", "addon": "agora_rtc", "extension_group": "default", "property": {"app_id": "${env:AGORA_APP_ID}", "token": "<agora_token>", "channel": "ten_agent_test", "stream_id": 1234, "remote_stream_id": 123, "subscribe_audio": true, "publish_audio": true, "publish_data": true, "enable_agora_asr": false, "agora_asr_vendor_name": "microsoft", "agora_asr_language": "en-US", "agora_asr_vendor_key": "${env:AZURE_STT_KEY|}", "agora_asr_vendor_region": "${env:AZURE_STT_REGION|}", "agora_asr_session_control_file_path": "session_control.conf", "subscribe_video_pix_fmt": 4, "subscribe_video": true, "max_memory_length": 10}}, {"type": "extension", "name": "stt", "addon": "transcribe_asr_python", "extension_group": "stt", "property": {"access_key": "${env:AWS_ACCESS_KEY_ID}", "lang_code": "en-US", "region": "us-east-1", "sample_rate": "16000", "secret_key": "${env:AWS_SECRET_ACCESS_KEY}"}}, {"type": "extension", "name": "llm", "addon": "bedrock_llm_python", "extension_group": "chatgpt", "property": {"access_key_id": "${env:AWS_ACCESS_KEY_ID}", "greeting": "TEN Agent connected. I am nova, How can I help you today?", "max_memory_length": 10, "max_tokens": 256, "model": "us.amazon.nova-lite-v1:0", "prompt": "Now you are an intelligent assistant with real-time interaction capabilities. I will provide you with a series of real-time video image information. Please understand these images as video frames. Based on the images and the user's input, engage in a conversation with the user, remembering the dialogue content in a concise and clear manner.", "region": "us-east-1", "secret_access_key": "${env:AWS_SECRET_ACCESS_KEY}", "temperature": 0.7, "topK": 10, "topP": 0.5, "is_memory_enabled": false, "is_enable_video": true}}, {"type": "extension", "name": "tts", "addon": "polly_tts", "extension_group": "tts", "property": {"region": "us-east-1", "access_key": "${env:AWS_ACCESS_KEY_ID}", "secret_key": "${env:AWS_SECRET_ACCESS_KEY}", "engine": "generative", "voice": "<PERSON>", "sample_rate": 16000, "lang_code": "en-US"}}, {"type": "extension", "name": "interrupt_detector", "addon": "interrupt_detector_python", "extension_group": "default", "property": {}}, {"type": "extension", "name": "message_collector", "addon": "message_collector", "extension_group": "transcriber", "property": {}}], "connections": [{"extension": "agora_rtc", "cmd": [{"name": "on_user_joined", "dest": [{"extension": "llm"}]}, {"name": "on_user_left", "dest": [{"extension": "llm"}]}, {"name": "on_connection_failure", "dest": [{"extension": "llm"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "stt"}]}], "video_frame": [{"name": "video_frame", "dest": [{"extension": "llm"}]}]}, {"extension": "stt", "data": [{"name": "text_data", "dest": [{"extension": "interrupt_detector"}, {"extension": "message_collector"}]}]}, {"extension": "llm", "cmd": [{"name": "flush", "dest": [{"extension": "tts"}]}], "data": [{"name": "text_data", "dest": [{"extension": "tts"}, {"extension": "message_collector"}]}]}, {"extension": "message_collector", "data": [{"name": "data", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "tts", "cmd": [{"name": "flush", "dest": [{"extension": "agora_rtc"}]}], "audio_frame": [{"name": "pcm_frame", "dest": [{"extension": "agora_rtc"}]}]}, {"extension": "interrupt_detector", "cmd": [{"name": "flush", "dest": [{"extension": "llm"}]}], "data": [{"name": "text_data", "dest": [{"extension": "llm"}]}]}]}], "log_level": 3}}