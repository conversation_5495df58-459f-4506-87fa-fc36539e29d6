services:
  ten_agent_dev:
    image: docker.theten.ai/ten-framework/ten_agent_build:0.6.4
    #image: ghcr.io/ten-framework/ten_agent_build:0.6.4
    container_name: ten_agent_dev
    platform: linux/amd64
    tty: true
    stdin_open: true
    restart: always
    ports:
      - "${GRAPH_DESIGNER_SERVER_PORT}:${GRAPH_DESIGNER_SERVER_PORT}"
      - "${SERVER_PORT}:${SERVER_PORT}"
    volumes:
      - ./:/app
      - ${LOG_PATH}:${LOG_PATH}
      - ../.vscode/:/app/.vscode
      - ../tools/pylint/.pylintrc:/tools/pylint/.pylintrc
    working_dir: /app
    env_file:
      - .env
    networks:
      - ten_agent_network
  ten_agent_playground:
    image: ghcr.io/ten-framework/ten_agent_playground:0.10.11-8-g94c71358
    container_name: ten_agent_playground
    restart: always
    ports:
      - "3000:3000"
    networks:
      - ten_agent_network
    environment:
      - AGENT_SERVER_URL=http://ten_agent_dev:8080
      - TEN_DEV_SERVER_URL=http://ten_agent_dev:49483
  ten_agent_demo:
    image: ghcr.io/ten-framework/ten_agent_demo:0.10.6-19-g8ecacde4
    container_name: ten_agent_demo
    restart: always
    ports:
      - "3002:3000"
    networks:
      - ten_agent_network
    environment:
      - AGENT_SERVER_URL=http://ten_agent_dev:8080
  # ten_graph_designer:
  #   image: ghcr.io/ten-framework/ten_graph_designer:4cc33b8
  #   container_name: ten_graph_designer
  #   restart: always
  #   ports:
  #     - "3001:3000"
  #   networks:
  #     - ten_agent_network
  #   environment:
  #     - TEN_DEV_SERVER_URL=http://ten_agent_dev:49483
networks:
  ten_agent_network:
    driver: bridge
