version: "3"

tasks:
  clean:
    desc: clean up
    cmds:
      - task: clean-agents
      - task: clean-server

  lint:
    desc: lint-agent
    env:
      PYTHONPATH: "./agents/ten_packages/system/ten_runtime_python/lib:./agents/ten_packages/system/ten_runtime_python/interface:./agents/ten_packages/system/ten_ai_base/interface"
    cmds:
      - ./agents/scripts/pylint.sh

  install-tools:
    desc: install tools
    cmds:
      - pip install pylint

  build:
    desc: build
    cmds:
      - task: build-agent
      - task: build-server

  use:
    desc: use agent, default 'agents/examples/default'
    vars:
      AGENT: '{{.AGENT| default "agents/examples/default"}}'
    cmds:
      - ln -sf {{.USER_WORKING_DIR}}/{{.AGENT}}/manifest.json ./agents/
      - ln -sf {{.USER_WORKING_DIR}}/{{.AGENT}}/property.json ./agents/
      - task: build

  run-server:
    desc: run backend http server
    cmds:
      - source .env && /app/server/bin/api

  run-gd-server:
    desc: run tman dev http server for graph designer
    dir: ./agents
    cmds:
      - export TMAN_08_COMPATIBLE=true && tman designer

  run:
    desc: run servers
    deps:
      - task: run-server
      - task: run-gd-server

  build-agent:
    desc: build agent
    dir: ./agents
    internal: true
    cmds:
      - ./scripts/install_deps_and_build.sh linux x64 && mv bin/main bin/worker

  build-server:
    desc: build server
    dir: ./server
    cmds:
      - go mod tidy && go mod download && go build -o bin/api main.go

  clean-agents:
    desc: clean up agents
    dir: ./agents
    internal: true
    cmds:
      - rm -rf manifest.json property.json manifest-lock.json bin/main bin/worker out .release ten_packages/system ten_packages/system/agora_rtc_sdk ten_packages/system/azure_speech_sdk ten_packages/system/nlohmann_json ten_packages/extension/agora_rtc ten_packages/extension/agora_rtm ten_packages/extension/agora_sess_ctrl ten_packages/extension/azure_tts ten_packages/addon_loader
      - find . -type d -name .pytest_cache -exec rm -rf {} \; || true
      - find . -type d -name __pycache__ -exec rm -rf {} \; || true
      - find . -type d -name .ten -exec rm -rf {} \; || true
      - find . -name .coverage -exec rm -f {} \; || true

  clean-server:
    desc: clean up server
    dir: ./server
    internal: true
    cmds:
      - rm -rf bin

  test:
    desc: run tests
    cmds:
      - task: test-agent-extensions
      - task: test-server

  test-server:
    desc: test server
    dir: ./server
    internal: true
    cmds:
      - go test -v ./...

  test-agent-extensions:
    desc: run standalone testing of extensions
    internal: true
    env:
      PYTHONPATH: "{{.USER_WORKING_DIR}}:{{.USER_WORKING_DIR}}/agents/ten_packages/system/ten_runtime_python/lib:{{.USER_WORKING_DIR}}/agents/ten_packages/system/ten_runtime_python/interface:{{.USER_WORKING_DIR}}/agents/ten_packages/system/ten_ai_base/interface"
    vars:
      EXTENSIONS:
        sh: 'find agents/ten_packages/extension -type d -exec test -d "{}/tests" \; -print'
    cmds:
      - for: { var: EXTENSIONS }
        task: test-extension
        vars:
          EXTENSION: "{{ .ITEM }}"

  test-extension:
    desc: run standalone testing of one single extension
    vars:
      EXTENSION: '{{.EXTENSION| default "agents/ten_packages/extension/elevenlabs_tts_python"}}'
    env:
      PYTHONPATH: "{{.USER_WORKING_DIR}}:{{.USER_WORKING_DIR}}/agents/ten_packages/system/ten_runtime_python/lib:{{.USER_WORKING_DIR}}/agents/ten_packages/system/ten_runtime_python/interface:{{.USER_WORKING_DIR}}/agents/ten_packages/system/ten_ai_base/interface"
    dotenv: [".env"]
    cmds:
      - cd {{.EXTENSION}} && tman -y install --standalone && ./tests/bin/start {{ .CLI_ARGS }}

  format:
    desc: format code
    cmds:
      - task: black-format

  black-format:
    desc: format python code with black
    internal: true
    cmds:
      - black --exclude "third_party/|agents/ten_packages/extension/http_server_python/|agents/ten_packages/system/ten_ai_base/interface/ten_ai_base/" --line-length 80 agents {{ .CLI_ARGS }}

  check:
    desc: check code
    cmds:
      - task: format-check

  format-check:
    desc: check code formatting
    cmds:
      - task: black-format-check

  black-format-check:
    desc: check code formatting with black
    internal: true
    cmds:
      - black --check --exclude "third_party/|agents/ten_packages/extension/http_server_python/|agents/ten_packages/system/ten_ai_base/interface/ten_ai_base/" --line-length 80 agents {{ .CLI_ARGS }}
