# ------------------------------
# Environment Variables for server & worker
# ------------------------------

# ------------------------------
# Server Configuration
# ------------------------------

# Log path
LOG_PATH=/tmp/ten_agent
LOG_STDOUT=true
# Graph designer server port
GRAPH_DESIGNER_SERVER_PORT=49483
# Server port
SERVER_PORT=8080
# Maximum number of workers
WORKERS_MAX=100
# Worker quit timeout in seconds
WORKER_QUIT_TIMEOUT_SECONDES=60

# Agora App ID
# Agora App Certificate(only required if enabled in the Agora Console)
AGORA_APP_ID=
AGORA_APP_CERTIFICATE=

# ------------------------------
# Worker Configuration
# ------------------------------

# Extension: aliyun_analyticdb_vector_storage
ALIBABA_CLOUD_ACCESS_KEY_ID=
ALIBABA_CLOUD_ACCESS_KEY_SECRET=
ALIYUN_ANALYTICDB_ACCOUNT=
ALIYUN_ANALYTICDB_ACCOUNT_PASSWORD=
ALIYUN_ANALYTICDB_INSTANCE_ID=
ALIYUN_ANALYTICDB_INSTANCE_REGION=cn-shanghai
ALIYUN_ANALYTICDB_NAMESPACE=
ALIYUN_ANALYTICDB_NAMESPACE_PASSWORD=

# Extension: aliyun_text_embedding
ALIYUN_TEXT_EMBEDDING_API_KEY=

# Extension: bedrock_llm
# Extension: polly_tts
AWS_ACCESS_KEY_ID=
AWS_SECRET_ACCESS_KEY=

# Extension: agora_rtc
# Azure STT key and region
AZURE_STT_KEY=
AZURE_STT_REGION=

# Extension: azure_tts
# Azure TTS key and region
AZURE_TTS_KEY=
AZURE_TTS_REGION=

# Extension: cartesia_tts
# Cartesia TTS key
CARTESIA_API_KEY=

# Extension: cosy_tts
# Cosy TTS key
COSY_TTS_KEY=

# Extension: deepgram_asr_python
# Deepgram ASR key
DEEPGRAM_API_KEY=

# Speechmatics API key
SPEECHMATICS_API_KEY=

# Extension: elevenlabs_tts
# ElevenLabs TTS key
ELEVENLABS_TTS_KEY=

# Extension: fish_audio_tts
# Fish.audio TTS key
FISH_AUDIO_TTS_KEY=

# Extension: gemini_llm
# Gemini API key
GEMINI_API_KEY=

# Extension: litellm
# Using Environment Variables, refer to https://docs.litellm.ai/docs/providers
# For example:
#     OpenAI
#         OPENAI_API_KEY=<your-api-key>
#         OPENAI_API_BASE=<openai-api-base>
#     AWS Bedrock
#         AWS_ACCESS_KEY_ID=<your-aws-access-key-id>
#         AWS_SECRET_ACCESS_KEY=<your-aws-secret-access-key>
#         AWS_REGION_NAME=<aws-region-name>
LITELLM_MODEL=gpt-4o-mini

# Extension: minimax_tts
# Minimax TTS key
MINIMAX_TTS_API_KEY=
MINIMAX_TTS_GROUP_ID=

# Extension: openai_chatgpt
# OpenAI API key
OPENAI_API_BASE=https://api.openai.com/v1
OPENAI_API_KEY=
OPENAI_MODEL=gpt-4o

# set this to azure if you are using azure openai
OPENAI_VENDOR=
OPENAI_AZURE_ENDPOINT=
OPENAI_AZURE_API_VERSION=

# OpenAI API key for realtime API
OPENAI_REALTIME_API_KEY=
# Azure OPENAI API key & Base URI for realtime API
AZURE_OPENAI_REALTIME_API_KEY=
AZURE_OPENAI_REALTIME_BASE_URI=

# OpenAI proxy URL
OPENAI_PROXY_URL=

# Extension: qwen_llm
# Qwen API key
QWEN_API_KEY=

# Extension: weatherapi_tool_python
# Weathers API key
WEATHERAPI_API_KEY=

# Extension: bingsearch_tool_python
# Bing search API key
BING_API_KEY=

# Extension: tsdb_firestore
# Firestore certifications
FIRESTORE_PROJECT_ID=
FIRESTORE_PRIVATE_KEY_ID=
FIRESTORE_PRIVATE_KEY=
FIRESTORE_CLIENT_EMAIL=
FIRESTORE_CLIENT_ID=
FIRESTORE_CERT_URL=

DEEPSEEK_API_KEY=

# Extension: dubverse_tts
# Dubverse TTS key
DUBVERSE_TTS_KEY=

# Groq Cloud API Key
GROQ_CLOUD_API_KEY=

# Azure AI Foundry API Key
AZURE_AI_FOUNDRY_BASE_URI=
AZURE_AI_FOUNDRY_API_KEY=


# Stepfun API Key
STEPFUN_API_KEY=