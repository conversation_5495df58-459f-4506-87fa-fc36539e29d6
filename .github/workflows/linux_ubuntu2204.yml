name: Linux (Ubuntu 22.04)

on:
  release:
    types: [created]
  pull_request:
    types: [opened, synchronize, reopened]
    paths-ignore:
      - "tools/**"
      - ".vscode/**"
      - ".devcontainer/**"
      - ".github/**"
      - "!.github/workflows/linux_ubuntu2204.yml"
      - "core/src/ten_manager/designer_frontend/**"
      - "**.md"
      - "ai_agents/**"

permissions:
  contents: write
  discussions: write
  security-events: write

concurrency:
  group: linux-intel-ubuntu2204-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: ubuntu-latest
    strategy:
      matrix:
        compiler: [gcc, clang]
        build_type: [debug, release]
        arch: [x64, x86]
        include:
          - compiler: gcc
            build_type: debug
            arch: x86
        exclude:
          - compiler: clang
            arch: x86
    container:
      image: ghcr.io/ten-framework/ten_building_ubuntu2204
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: false

      - name: Trust working directory
        run: git config --global --add safe.directory "${GITHUB_WORKSPACE}"

      - name: Initialize and update submodules except portal/
        run: |
          # Retrieve all submodule paths, excluding `portal/`.
          submodules=$(git config --file .gitmodules --get-regexp path | awk '$2 != "portal" { print $2 }')

          git submodule init

          for submodule in $submodules; do
            echo "Initializing submodule: $submodule"
            git submodule update --init --recursive --depth 1 "$submodule"
          done

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Update version
        run: |
          python3 tools/version/update_version_in_ten_framework.py
          python3 tools/version/check_version_in_ten_framework.py

      - name: Install gcc-multilib and g++-multilib
        if: matrix.arch == 'x86'
        run: |
          apt-get update
          apt-get install -y gcc-multilib g++-multilib

      - name: Build
        run: |
          export PATH=$(pwd)/core/ten_gn:/usr/local/go/bin:/root/go/bin:/root/.cargo/bin:$PATH
          echo $PATH

          if [ "${{ matrix.arch }}" = "x64" ]; then
            go env -w GOFLAGS="-buildvcs=false"
            go install golang.org/dl/go1.24.3@latest
            export PATH=$PATH:$(go env GOPATH)/bin
            go1.24.3 download
            go1.24.3 version
            rustup default nightly
          fi

          df -h .

          if [ "${{ matrix.arch }}" = "x86" ]; then
            EXTRA_ARGS="is_clang=false ten_enable_ten_rust=false ten_enable_ten_manager=false ten_manager_enable_tests=false ten_enable_go_binding=false ten_enable_python_binding=false ten_enable_nodejs_binding=false ten_manager_enable_frontend=false ten_enable_integration_tests_prebuilt=false"
          else
            EXTRA_ARGS="is_clang=${{ matrix.compiler == 'gcc' && 'false' || 'true' }} log_level=1 enable_serialized_actions=true ten_enable_serialized_rust_action=true ten_rust_enable_gen_cargo_config=false ten_enable_cargo_clean=true ten_enable_go_lint=true ten_enable_rust_incremental_build=false ten_manager_enable_frontend=false ten_enable_integration_tests_prebuilt=false ten_enable_ffmpeg_extensions=true"
          fi

          echo $EXTRA_ARGS

          tgn gen linux ${{ matrix.arch }} ${{ matrix.build_type }} -- $EXTRA_ARGS
          tgn build linux ${{ matrix.arch }} ${{ matrix.build_type }}

          df -h .
          tree -I 'gen|obj' out

      - name: Update supports before upload or publish
        if: matrix.arch == 'x64'
        run: |
          UPDATE_SUPPORTS_SCRIPT=$(pwd)/tools/supports/update_supports_in_manifest_json.py

          cd out/linux/${{ matrix.arch }}/ten_packages
          ARRAY=(
            "system/ten_runtime"
            "system/ten_runtime_go"
            "system/ten_runtime_python"
            "addon_loader/python_addon_loader"
          )

          for item in "${ARRAY[@]}"; do
            python3 ${UPDATE_SUPPORTS_SCRIPT} --os-arch-pairs linux:x64 --input-file ${item}/manifest.json --output-file ${item}/manifest.json --log-level 1
            cat ${item}/manifest.json
          done

          df -h .
        shell: bash

        # Package the tests artifacts into a tar file while preserving file
        # permissions.
      - name: Package tests relevant artifacts preserving permissions
        run: |
          files=""
          for item in tests ten_manager tgn_args.txt; do
            if [ -e "out/linux/${{ matrix.arch }}/$item" ]; then
              files="$files out/linux/${{ matrix.arch }}/$item"
            fi
          done

          if [ -n "$files" ]; then
            tar -czvf tests-artifacts.tar.gz $files
          fi

      - name: Upload tests relevant artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tests-artifacts-${{ matrix.compiler }}-${{ matrix.build_type }}-${{ matrix.arch }}
          path: tests-artifacts.tar.gz
          if-no-files-found: ignore

      - name: Package assets
        if: matrix.arch == 'x64' && startsWith(github.ref, 'refs/tags/')
        run: |
          cd out/linux/${{ matrix.arch }}
          zip -vr ten_packages-linux-${{ matrix.compiler }}-${{ matrix.build_type }}-${{ matrix.arch }}.zip \
            app/default_app_cpp \
            app/default_app_go \
            app/default_app_python \
            app/default_app_nodejs \
            ten_packages/system/ten_runtime \
            ten_packages/system/ten_runtime_go \
            ten_packages/system/ten_runtime_python \
            ten_packages/system/ten_runtime_nodejs \
            ten_packages/extension/default_extension_cpp \
            ten_packages/extension/default_extension_go \
            ten_packages/extension/default_extension_python \
            ten_packages/extension/default_async_extension_python \
            ten_packages/extension/default_extension_nodejs \
            ten_packages/addon_loader/python_addon_loader \
            ten_packages/addon_loader/nodejs_addon_loader

          df -h .

      - name: Publish to release assets
        uses: softprops/action-gh-release@v2
        if: matrix.arch == 'x64' && startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            out/linux/${{ matrix.arch }}/ten_packages-linux-${{ matrix.compiler }}-${{ matrix.build_type }}-${{ matrix.arch }}.zip

      - name: Clean up
        if: matrix.arch == 'x64' && startsWith(github.ref, 'refs/tags/')
        run: |
          rm -rf out/linux/${{ matrix.arch }}/ten_packages-linux-${{ matrix.compiler }}-${{ matrix.build_type }}-${{ matrix.arch }}.zip

          df -h .

      - name: Publish release to TEN cloud store
        if: ${{ matrix.arch == 'x64' && startsWith(github.ref, 'refs/tags/') && matrix.compiler == 'gcc' && matrix.build_type == 'release' && matrix.arch == 'x64' }}
        run: |
          TMAN_BIN=$(pwd)/out/linux/${{ matrix.arch }}/ten_manager/bin/tman

          cd out/linux/${{ matrix.arch }}
          ARRAY=(
            "app/default_app_cpp"
            "app/default_app_go"
            "app/default_app_python"
            "app/default_app_nodejs"
            "ten_packages/system/ten_runtime"
            "ten_packages/system/ten_runtime_go"
            "ten_packages/system/ten_runtime_python"
            "ten_packages/system/ten_runtime_nodejs"
            "ten_packages/extension/default_extension_cpp"
            "ten_packages/extension/default_extension_go"
            "ten_packages/extension/default_extension_python"
            "ten_packages/extension/default_async_extension_python"
            "ten_packages/extension/default_extension_nodejs"
            "ten_packages/addon_loader/python_addon_loader"
            "ten_packages/addon_loader/nodejs_addon_loader"
          )

          for item in "${ARRAY[@]}"; do
            echo $item
            cd $item
            identity=$(${TMAN_BIN} package --get-identity)
            echo $identity
            ${TMAN_BIN} --verbose --admin-token ${{ secrets.TEN_CLOUD_STORE_ADMIN_TOKEN }} delete $identity || true
            ${TMAN_BIN} --verbose --user-token ${{ secrets.TEN_CLOUD_STORE }} publish
            cd -
          done

          df -h .
        shell: bash

      - name: Clean up
        if: ${{ matrix.arch == 'x64' && startsWith(github.ref, 'refs/tags/') && matrix.compiler == 'gcc' && matrix.build_type == 'release' && matrix.arch == 'x64' }}
        run: |
          cd out/linux/${{ matrix.arch }}
          ARRAY=(
            "app/default_app_cpp"
            "app/default_app_go"
            "app/default_app_python"
            "app/default_app_nodejs"
            "ten_packages/system/ten_runtime"
            "ten_packages/system/ten_runtime_go"
            "ten_packages/system/ten_runtime_python"
            "ten_packages/system/ten_runtime_nodejs"
            "ten_packages/extension/default_extension_cpp"
            "ten_packages/extension/default_extension_go"
            "ten_packages/extension/default_extension_python"
            "ten_packages/extension/default_async_extension_python"
            "ten_packages/extension/default_extension_nodejs"
            "ten_packages/addon_loader/python_addon_loader"
            "ten_packages/addon_loader/nodejs_addon_loader"
          )

          for item in "${ARRAY[@]}"; do
            rm -rf $item
          done

          df -h .
        shell: bash

  test-standalone:
    needs: build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        compiler: [gcc, clang]
        build_type: [debug, release]
        arch: [x64, x86]
        include:
          - compiler: gcc
            build_type: debug
            arch: x86
        exclude:
          - compiler: clang
            arch: x86
    container:
      image: ghcr.io/ten-framework/ten_building_ubuntu2204
    steps:
      - name: Download build artifacts (tar archive)
        uses: actions/download-artifact@v4
        with:
          name: tests-artifacts-${{ matrix.compiler }}-${{ matrix.build_type }}-${{ matrix.arch }}
          path: out/linux/${{ matrix.arch }}

      - name: Extract tests artifacts preserving permissions
        run: |
          tar -xzf out/linux/${{ matrix.arch }}/tests-artifacts.tar.gz

      - name: View folder structure content
        run: |
          df -h .
          tree -I ".*|*.h|*.hpp|*.py" out/linux/${{ matrix.arch }}

      - name: Set ulimit and sysctl
        run: |
          # Because there are concurrent test cases which involves many
          # concurrent socket connections, we need to enlarge the maximum number
          # of the opened file descriptor.
          ulimit -n 102400

          # Because there are concurrent test cases (in 'smoke' and
          # 'integration') which will create many concurrent connections
          # simutaneously, we increase the TCP listening backlog value to 8192.
          sysctl -w net.core.somaxconn=8192
        shell: bash

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Update tman config.json
        if: matrix.arch == 'x64'
        run: |
          CONFIG_FILE="out/linux/${{ matrix.arch }}/tests/local_registry/config.json"
          echo "Before update:"
          cat $CONFIG_FILE
          sed -i "s|\(file://\)[^\"]*\(out\/linux\/${{ matrix.arch }}\/tests\/local_registry\)|\1${GITHUB_WORKSPACE}/\2|" $CONFIG_FILE
          echo "After update:"
          cat $CONFIG_FILE

      - name: Run Tests (ten_utils_unit_test)
        env:
          ASAN_OPTIONS: detect_leaks=1:detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          chmod +x out/linux/${{ matrix.arch }}/tests/standalone/ten_utils_unit_test
          out/linux/${{ matrix.arch }}/tests/standalone/ten_utils_unit_test || { echo "test failed"; exit 1; }

          df -h .

      - name: Clean up
        run: |
          rm -rf out/linux/${{ matrix.arch }}/tests/standalone/ten_utils_unit_test

          df -h .

      - name: Run Tests (ten_runtime_unit_test)
        env:
          ASAN_OPTIONS: detect_leaks=1:detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          chmod +x out/linux/${{ matrix.arch }}/tests/standalone/ten_runtime_unit_test
          out/linux/${{ matrix.arch }}/tests/standalone/ten_runtime_unit_test || { echo "test failed"; exit 1; }

          df -h

      - name: Clean up
        run: |
          rm -rf out/linux/${{ matrix.arch }}/tests/standalone/ten_runtime_unit_test

          df -h .

      - name: Run Tests (ten_rust standalone tests)
        if: matrix.arch == 'x64'
        env:
          ASAN_OPTIONS: detect_leaks=1:detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          cd out/linux/${{ matrix.arch }}/tests/standalone/ten_rust

          chmod +x unit_test
          chmod +x integration_test

          ./unit_test --nocapture || { echo "ten_rust unit test failed"; exit 1; }
          ./integration_test --nocapture || { echo "ten_rust integration test failed"; exit 1; }

          df -h .

      - name: Clean up
        if: matrix.arch == 'x64'
        run: |
          rm -rf out/linux/${{ matrix.arch }}/tests/standalone/ten_rust

          df -h .

      - name: Run Tests (ten_manager standalone tests)
        if: matrix.arch == 'x64'
        env:
          ASAN_OPTIONS: detect_leaks=1:detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          cd out/linux/${{ matrix.arch }}/tests/standalone/ten_manager

          chmod +x unit_test
          chmod +x integration_test

          ./unit_test --nocapture || { echo "ten_manager unit test failed"; exit 1; }
          ./integration_test --nocapture || { echo "ten_manager integration test failed"; exit 1; }

          df -h .

      - name: Clean up
        if: matrix.arch == 'x64'
        run: |
          rm -rf out/linux/${{ matrix.arch }}/tests/standalone/ten_manager

          df -h .

      - name: Run Tests (ten_runtime_smoke_test)
        env:
          ASAN_OPTIONS: detect_leaks=1:detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          chmod +x out/linux/${{ matrix.arch }}/tests/standalone/ten_runtime_smoke_test
          out/linux/${{ matrix.arch }}/tests/standalone/ten_runtime_smoke_test || { echo "test failed"; exit 1; }

          df -h .

      - name: Clean up
        run: |
          rm -rf out/linux/${{ matrix.arch }}/tests/standalone/ten_runtime_smoke_test

          df -h .

  test-integration:
    needs: build
    runs-on: ubuntu-latest
    strategy:
      matrix:
        compiler: [gcc, clang]
        build_type: [debug, release]
        arch: [x64, x86]
        include:
          - compiler: gcc
            build_type: debug
            arch: x86
        exclude:
          - compiler: clang
            arch: x86
    container:
      image: ghcr.io/ten-framework/ten_building_ubuntu2204
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: false

      - name: Trust working directory
        run: git config --global --add safe.directory "${GITHUB_WORKSPACE}"

      - name: Initialize and update submodules except portal/
        run: |
          # Retrieve all submodule paths, excluding `portal/`.
          submodules=$(git config --file .gitmodules --get-regexp path | awk '$2 != "portal" { print $2 }')

          git submodule init

          for submodule in $submodules; do
            echo "Initializing submodule: $submodule"
            git submodule update --init --recursive --depth 1 "$submodule"
          done

      - name: Download build artifacts (tar archive)
        uses: actions/download-artifact@v4
        with:
          name: tests-artifacts-${{ matrix.compiler }}-${{ matrix.build_type }}-${{ matrix.arch }}
          path: out/linux/${{ matrix.arch }}

      - name: Extract tests artifacts preserving permissions
        run: |
          tar -xzf out/linux/${{ matrix.arch }}/tests-artifacts.tar.gz

      - name: View folder structure content
        run: |
          df -h .
          tree -I ".*|*.h|*.hpp|*.py" out/linux/${{ matrix.arch }}

      - name: Set ulimit and sysctl
        run: |
          # Because there are concurrent test cases which involves many
          # concurrent socket connections, we need to enlarge the maximum number
          # of the opened file descriptor.
          ulimit -n 102400

          # Because there are concurrent test cases (in 'smoke' and
          # 'integration') which will create many concurrent connections
          # simutaneously, we increase the TCP listening backlog value to 8192.
          sysctl -w net.core.somaxconn=8192
        shell: bash

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - name: Update tman config.json
        if: matrix.arch == 'x64'
        run: |
          CONFIG_FILE="out/linux/${{ matrix.arch }}/tests/local_registry/config.json"
          echo "Before update:"
          cat $CONFIG_FILE
          sed -i "s|\(file://\)[^\"]*\(out\/linux\/${{ matrix.arch }}\/tests\/local_registry\)|\1${GITHUB_WORKSPACE}/\2|" $CONFIG_FILE
          echo "After update:"
          cat $CONFIG_FILE

      - name: Install Python dependencies via script
        run: |
          python .github/tools/setup_pytest_dependencies.py

          df -h .

      - name: Run Tests (ten_manager pytest tests)
        if: matrix.arch == 'x64'
        env:
          ASAN_OPTIONS: detect_leaks=1:detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          cd out/linux/${{ matrix.arch }}/
          pytest -s tests/ten_manager/

          df -h .

      - name: Clean up
        if: matrix.arch == 'x64'
        run: |
          rm -rf out/linux/${{ matrix.arch }}/tests/ten_manager

          df -h .

      - name: Run tests (ten_runtime integration tests)
        if: matrix.arch == 'x64'
        env:
          ASAN_OPTIONS: detect_leaks=1:detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
          GOTRACEBACK: crash
        run: |
          curl -fsSL https://ollama.com/install.sh | sh

          # Start Ollama service.
          ollama serve &

          # Wait for Ollama to be fully operational.
          for i in {1..30}; do
            if curl -s http://localhost:11434 >/dev/null; then
              echo "✓ Ollama is running"
              break
            fi
            echo "Waiting for Ollama to start..."
            sleep 2
          done

          ollama pull smollm:135m

          # Verify model is pulled correctly.
          ollama list

          df -h .

          export PATH=$(pwd)/core/ten_gn:$PATH
          cd out/linux/${{ matrix.arch }}/
          pytest -s tests/ten_runtime/integration/

          df -h .
