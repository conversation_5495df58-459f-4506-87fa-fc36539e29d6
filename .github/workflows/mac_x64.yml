name: <PERSON> (x64)

on:
  release:
    types: [created]
  pull_request:
    types: [opened, synchronize, reopened]
    paths-ignore:
      - "tools/**"
      - ".vscode/**"
      - ".devcontainer/**"
      - ".github/**"
      - "!.github/workflows/mac_x64.yml"
      - "core/src/ten_manager/designer_frontend/**"
      - "**.md"
      - "ai_agents/**"

permissions:
  contents: write
  discussions: write
  security-events: write

concurrency:
  group: mac-x64-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: macos-13
    strategy:
      matrix:
        build_type: [release]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: false

      - name: Trust working directory
        run: git config --global --add safe.directory "${GITHUB_WORKSPACE}"

      - name: Initialize and update submodules except portal/
        run: |
          # Retrieve all submodule paths, excluding `portal/`.
          submodules=$(git config --file .gitmodules --get-regexp path | awk '$2 != "portal" { print $2 }')

          git submodule init

          for submodule in $submodules; do
            echo "Initializing submodule: $submodule"
            git submodule update --init --recursive --depth 1 "$submodule"
          done

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - uses: actions/setup-go@v5
        with:
          go-version: "stable"
          cache: false

      - name: Install tools and dependencies
        run: |
          brew install llvm
          rustup default nightly

          brew install tree
          brew install gettext

          pip3 install --use-pep517 python-dotenv jinja2

          go install golang.org/dl/go1.24.3@latest && go1.24.3 download
          go env -w GOFLAGS="-buildvcs=false"

          cargo install --force cbindgen

      - name: Build
        run: |
          if [ ${{ matrix.build_type }} == "debug" ]; then
            export PATH="/usr/local/opt/llvm/bin:$PATH"
          fi
          export PATH=$(pwd)/core/ten_gn:$PATH
          echo $PATH

          df -h .

          tgn gen mac x64 ${{ matrix.build_type }} -- log_level=1 enable_serialized_actions=true ten_enable_serialized_rust_action=true ten_rust_enable_gen_cargo_config=false ten_enable_cargo_clean=true ten_enable_python_binding=true ten_enable_advanced_python_integration_tests=false ten_enable_rust_incremental_build=false ten_manager_enable_frontend=false ten_enable_integration_tests_prebuilt=false ten_enable_nodejs_binding=false

          tgn build mac x64 ${{ matrix.build_type }}

          df -h .
          tree -I 'gen|obj' out
      #   continue-on-error: true
      # - name: Setup tmate session
      #   uses: mxschmitt/action-tmate@v3

      # Package the tests artifacts into a tar file while preserving file
      # permissions.
      - name: Package tests relevant artifacts preserving permissions
        run: |
          files=""
          for item in tests ten_manager tgn_args.txt; do
            if [ -e "out/mac/x64/$item" ]; then
              files="$files out/mac/x64/$item"
            fi
          done

          if [ -n "$files" ]; then
            tar -czvf tests-artifacts.tar.gz $files
          fi

      - name: Upload tests relevant artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tests-artifacts-${{ matrix.build_type }}
          path: tests-artifacts.tar.gz
          if-no-files-found: ignore

      - name: Package assets
        if: startsWith(github.ref, 'refs/tags/')
        run: |
          cd out/mac/x64
          zip -vr ten_packages-mac-${{ matrix.build_type }}-x64.zip \
            app/default_app_cpp \
            app/default_app_go \
            ten_packages/system/ten_runtime \
            ten_packages/system/ten_runtime_go \
            ten_packages/extension/default_extension_cpp \
            ten_packages/extension/default_extension_go \

          df -h .

      - name: Publish to release assets
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            out/mac/x64/ten_packages-mac-${{ matrix.build_type }}-x64.zip

  test-standalone:
    needs: build
    runs-on: macos-13
    strategy:
      matrix:
        build_type: [release]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: false

      - name: Trust working directory
        run: git config --global --add safe.directory "${GITHUB_WORKSPACE}"

      - name: Initialize and update submodules except portal/
        run: |
          # Retrieve all submodule paths, excluding `portal/`.
          submodules=$(git config --file .gitmodules --get-regexp path | awk '$2 != "portal" { print $2 }')
          git submodule init
          for submodule in $submodules; do
            echo "Initializing submodule: $submodule"
            git submodule update --init --recursive --depth 1 "$submodule"
          done

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - uses: actions/setup-go@v5
        with:
          go-version: "stable"
          cache: false

      - name: Set ulimit and sysctl
        run: |
          # Because there are concurrent test cases which involves many
          # concurrent socket connections, we need to enlarge the maximum number
          # of the opened file descriptor.
          ulimit -n 102400
          # Because there are concurrent test cases which will create many
          # concurrent connections simutaneously, we increase the TCP listening
          # backlog value to 8192.
          sudo sysctl -w kern.ipc.somaxconn=8192
        shell: bash

      - name: Install tools and dependencies
        run: |
          brew install llvm
          rustup default nightly

          brew install tree
          pip3 install --use-pep517 python-dotenv jinja2
          go install golang.org/dl/go1.24.3@latest && go1.24.3 download
          go env -w GOFLAGS="-buildvcs=false"
          cargo install --force cbindgen

      - name: Download build artifacts (tar archive)
        uses: actions/download-artifact@v4
        with:
          name: tests-artifacts-${{ matrix.build_type }}
          path: out/mac/x64

      - name: Extract tests artifacts preserving permissions
        run: |
          tar -xzf out/mac/x64/tests-artifacts.tar.gz

      - name: View folder structure content
        run: |
          df -h .
          tree -I ".*|*.h|*.hpp|*.py" out/mac/x64

      - name: Run Tests (ten_utils_unit_test)
        env:
          ASAN_OPTIONS: detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          chmod +x out/mac/x64/tests/standalone/ten_utils_unit_test
          out/mac/x64/tests/standalone/ten_utils_unit_test
          df -h .

      - name: Run Tests (ten_runtime_unit_test)
        env:
          ASAN_OPTIONS: detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          chmod +x out/mac/x64/tests/standalone/ten_runtime_unit_test
          out/mac/x64/tests/standalone/ten_runtime_unit_test
          df -h .

      - name: Run Tests (ten_runtime_smoke_test)
        env:
          ASAN_OPTIONS: detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          chmod +x out/mac/x64/tests/standalone/ten_runtime_smoke_test
          out/mac/x64/tests/standalone/ten_runtime_smoke_test
          df -h .

      - name: Run Tests (ten_rust standalone tests)
        env:
          ASAN_OPTIONS: detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          cd out/mac/x64/tests/standalone/ten_rust

          chmod +x unit_test
          chmod +x integration_test

          ./unit_test --nocapture || { echo "ten_rust unit test failed"; exit 1; }
          ./integration_test --nocapture || { echo "ten_rust integration test failed"; exit 1; }

          df -h .

      - name: Run Tests (ten_manager standalone tests)
        env:
          ASAN_OPTIONS: detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          cd out/mac/x64/tests/standalone/ten_manager

          chmod +x unit_test
          chmod +x integration_test

          ./unit_test --nocapture || { echo "ten_manager unit test failed"; exit 1; }
          ./integration_test --nocapture || { echo "ten_manager integration test failed"; exit 1; }

          df -h .

  test-integration:
    needs: build
    runs-on: macos-13
    strategy:
      matrix:
        build_type: [release]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: false

      - name: Trust working directory
        run: git config --global --add safe.directory "${GITHUB_WORKSPACE}"

      - name: Initialize and update submodules except portal/
        run: |
          # Retrieve all submodule paths, excluding `portal/`.
          submodules=$(git config --file .gitmodules --get-regexp path | awk '$2 != "portal" { print $2 }')

          git submodule init

          for submodule in $submodules; do
            echo "Initializing submodule: $submodule"
            git submodule update --init --recursive --depth 1 "$submodule"
          done

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - uses: actions/setup-go@v5
        with:
          go-version: "stable"
          cache: false

      - name: Set ulimit and sysctl
        run: |
          # Because there are concurrent test cases which involves many
          # concurrent socket connections, we need to enlarge the maximum number
          # of the opened file descriptor.
          ulimit -n 102400

          # Because there are concurrent test cases which will create many
          # concurrent connections simutaneously, we increase the TCP listening
          # backlog value to 8192.
          sudo sysctl -w kern.ipc.somaxconn=8192
        shell: bash

      - name: Install tools and dependencies
        run: |
          brew install llvm

          brew install tree
          pip3 install --use-pep517 python-dotenv jinja2
          go install golang.org/dl/go1.24.3@latest && go1.24.3 download
          go env -w GOFLAGS="-buildvcs=false"

      - name: Download build artifacts (tar archive)
        uses: actions/download-artifact@v4
        with:
          name: tests-artifacts-${{ matrix.build_type }}
          path: out/mac/x64

      - name: Extract tests artifacts preserving permissions
        run: |
          tar -xzf out/mac/x64/tests-artifacts.tar.gz

      - name: View folder structure content
        run: |
          df -h .
          tree -I ".*|*.h|*.hpp|*.py" out/mac/x64

      - name: Install Python dependencies via script
        run: |
          python .github/tools/setup_pytest_dependencies.py

          df -h .

      - name: Run Tests (ten_manager pytest tests)
        env:
          ASAN_OPTIONS: detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          cd out/mac/x64/
          pytest -s tests/ten_manager/

          df -h .

      - name: Run tests (ten_runtime integration tests)
        env:
          ASAN_OPTIONS: detect_stack_use_after_return=1:color=always:unmap_shadow_on_exit=1:abort_on_error=1
          MALLOC_CHECK_: 3
          TEN_ENABLE_MEMORY_TRACKING: "true"
          TEN_ENABLE_BACKTRACE_DUMP: "true"
          GOTRACEBACK: crash
        run: |
          if [ ${{ matrix.build_type }} == "debug" ]; then
            export PATH="/usr/local/opt/llvm/bin:$PATH"
          fi
          export PATH=$(pwd)/core/ten_gn:$PATH
          cd out/mac/x64/
          pytest -s tests/ten_runtime/integration/

          df -h .
      #   continue-on-error: true
      # - name: Setup tmate session
      #   uses: mxschmitt/action-tmate@v3
