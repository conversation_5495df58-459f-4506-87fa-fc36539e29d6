name: Windows (x64)

on:
  release:
    types: [created]
  pull_request:
    types: [opened, synchronize, reopened]
    paths-ignore:
      - "tools/**"
      - ".vscode/**"
      - ".devcontainer/**"
      - ".github/**"
      - "!.github/workflows/win.yml"
      - "core/src/ten_manager/designer_frontend/**"
      - "**.md"
      - "ai_agents/**"

permissions:
  contents: write
  discussions: write
  security-events: write

concurrency:
  group: win-x64-${{ github.head_ref }}
  cancel-in-progress: true

jobs:
  build:
    runs-on: windows-latest
    env:
      PYTHONIOENCODING: utf-8
    strategy:
      matrix:
        build_type: [debug, release]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: false

      - name: Trust working directory
        run: git config --global --add safe.directory "${GITHUB_WORKSPACE}"

      - name: Initialize and update submodules except portal/
        shell: bash
        run: |
          # Retrieve all submodule paths, excluding `portal/`.
          submodules=$(git config --file .gitmodules --get-regexp path | awk '$2 != "portal" { print $2 }')

          git submodule init

          for submodule in $submodules; do
            echo "Initializing submodule: $submodule"
            git submodule update --init --recursive --depth 1 "$submodule"
          done

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: ilammy/msvc-dev-cmd@v1

      - uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - uses: actions/setup-go@v5
        with:
          go-version: "stable"
          cache: false

      - name: Install tools and dependencies
        run: |
          pip3 install --use-pep517 python-dotenv jinja2
          go install golang.org/dl/go1.24.3@latest && go1.24.3 download
          go env -w GOFLAGS="-buildvcs=false"
          rustup default nightly
          cargo install --force cbindgen

      - name: Get Python executable path
        run: |
          $pythonPath = python -c "import sys; print(sys.executable)"
          Write-Output "Python executable path: $pythonPath"

          $pythonDir = Split-Path $pythonPath
          Write-Output "Python directory path: $pythonDir"

          echo "PYTHON3_PATH=$pythonDir" | Out-File -FilePath $env:GITHUB_ENV -Encoding utf8 -Append
        shell: pwsh

      - name: Use Python path
        run: |
          Write-Output "The Python directory is located at: $env:PYTHON3_PATH"
        shell: pwsh

      - name: Build
        run: |
          $ENV:PATH += ";$PWD/core/ten_gn"
          tgn gen win x64 ${{ matrix.build_type }} -- vs_version=2022 log_level=1 enable_serialized_actions=true ten_enable_serialized_rust_action=true ten_rust_enable_gen_cargo_config=false ten_enable_cargo_clean=true ten_enable_python_binding=false ten_enable_go_binding=false ten_enable_nodejs_binding=false ten_enable_rust_incremental_build=false ten_manager_enable_frontend=false ten_enable_integration_tests_prebuilt=false
          tgn build win x64 ${{ matrix.build_type }}

      - name: Upload ten_packages
        uses: actions/upload-artifact@v4
        with:
          name: ten_packages-win-${{ matrix.build_type }}-x64
          path: |
            out/win/x64/app/default_app_cpp
            out/win/x64/ten_packages/system/ten_runtime
            out/win/x64/ten_packages/extension/default_extension_cpp

      - name: Package assets
        if: startsWith(github.ref, 'refs/tags/')
        shell: pwsh
        working-directory: out/win/x64
        run: |
          Write-Host "Current directory: $(Get-Location)"

          Compress-Archive -Path ten_manager -DestinationPath tman-win-${{ matrix.build_type }}-x64.zip -CompressionLevel Optimal

          Compress-Archive -Path app/default_app_cpp, ten_packages/system/ten_runtime, ten_packages/extension/default_extension_cpp -DestinationPath ten_packages-win-${{ matrix.build_type }}-x64.zip -CompressionLevel Optimal

      - name: Publish to release assets
        uses: softprops/action-gh-release@v2
        if: startsWith(github.ref, 'refs/tags/')
        with:
          files: |
            out/win/x64/tman-win-${{ matrix.build_type }}-x64.zip
            out/win/x64/ten_packages-win-${{ matrix.build_type }}-x64.zip

      - name: Package tests relevant artifacts preserving permissions
        shell: pwsh
        run: |
          $basePath = "out/win/x64"
          $items = @("tests", "ten_manager", "tgn_args.txt")
          $files = @()

          foreach ($item in $items) {
            $path = Join-Path $basePath $item
            if (Test-Path $path) {
              $files += $path
            }
          }

          if ($files.Count -gt 0) {
            Compress-Archive -Path $files -DestinationPath tests-artifacts.zip -Force
          }

      - name: Upload tests relevant artifacts
        uses: actions/upload-artifact@v4
        with:
          name: tests-artifacts-${{ matrix.build_type }}
          path: tests-artifacts.zip
          if-no-files-found: ignore

  test-standalone:
    needs: build
    runs-on: windows-latest
    env:
      PYTHONIOENCODING: utf-8
    strategy:
      matrix:
        build_type: [debug, release]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: false

      - name: Trust working directory
        run: git config --global --add safe.directory "${GITHUB_WORKSPACE}"

      - name: Initialize and update submodules except portal/
        shell: bash
        run: |
          # Retrieve all submodule paths, excluding `portal/`.
          submodules=$(git config --file .gitmodules --get-regexp path | awk '$2 != "portal" { print $2 }')
          git submodule init
          for submodule in $submodules; do
            echo "Initializing submodule: $submodule"
            git submodule update --init --recursive --depth 1 "$submodule"
          done

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: ilammy/msvc-dev-cmd@v1

      - uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - uses: actions/setup-go@v5
        with:
          go-version: "stable"
          cache: false

      - name: Install tools and dependencies
        run: |
          pip3 install --use-pep517 python-dotenv jinja2
          go install golang.org/dl/go1.24.3@latest && go1.24.3 download
          go env -w GOFLAGS="-buildvcs=false"
          rustup default nightly
          cargo install --force cbindgen

      - name: Get Python executable path
        run: |
          $pythonPath = python -c "import sys; print(sys.executable)"
          Write-Output "Python executable path: $pythonPath"
          $pythonDir = Split-Path $pythonPath
          Write-Output "Python directory path: $pythonDir"
          echo "PYTHON3_PATH=$pythonDir" | Out-File -FilePath $env:GITHUB_ENV -Encoding utf8 -Append
        shell: pwsh

      - name: Use Python path
        run: |
          Write-Output "The Python directory is located at: $env:PYTHON3_PATH"
        shell: pwsh

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: tests-artifacts-${{ matrix.build_type }}
          path: out/win/x64

      - name: Extract tests artifacts preserving permissions
        shell: pwsh
        run: |
          Expand-Archive -Path "out/win/x64/tests-artifacts.zip" -DestinationPath "out/win/x64" -Force

      - name: Run Tests (ten_utils_unit_test)
        env:
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          chmod +x out/win/x64/tests/standalone/ten_utils_unit_test
          out/win/x64/tests/standalone/ten_utils_unit_test

      - name: Run Tests (ten_runtime_unit_test)
        env:
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          chmod +x out/win/x64/tests/standalone/ten_runtime_unit_test
          out/win/x64/tests/standalone/ten_runtime_unit_test

      - name: Run Tests (ten_rust standalone tests)
        run: |
          cd out/win/x64/tests/standalone/ten_rust

          chmod +x unit_test
          chmod +x integration_test

          ./unit_test --nocapture || { echo "ten_rust unit test failed"; exit 1; }
          ./integration_test --nocapture || { echo "ten_rust integration test failed"; exit 1; }

      - name: Run Tests (ten_manager standalone tests)
        run: |
          cd out/win/x64/tests/standalone/ten_manager

          chmod +x unit_test
          chmod +x integration_test

          ./unit_test --nocapture || { echo "ten_manager unit test failed"; exit 1; }
          ./integration_test --nocapture || { echo "ten_manager integration test failed"; exit 1; }

      #   continue-on-error: true
      # - name: Setup tmate session
      #   uses: mxschmitt/action-tmate@v3

      - name: Run Tests (ten_runtime_smoke_test)
        env:
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          chmod +x out/win/x64/tests/standalone/ten_runtime_smoke_test
          out/win/x64/tests/standalone/ten_runtime_smoke_test

  test-integration:
    needs: build
    runs-on: windows-latest
    env:
      PYTHONIOENCODING: utf-8
    strategy:
      matrix:
        build_type: [debug, release]
    steps:
      - uses: actions/checkout@v4
        with:
          fetch-depth: 0
          submodules: false

      - name: Trust working directory
        run: git config --global --add safe.directory "${GITHUB_WORKSPACE}"

      - name: Initialize and update submodules except portal/
        shell: bash
        run: |
          # Retrieve all submodule paths, excluding `portal/`.
          submodules=$(git config --file .gitmodules --get-regexp path | awk '$2 != "portal" { print $2 }')
          git submodule init
          for submodule in $submodules; do
            echo "Initializing submodule: $submodule"
            git submodule update --init --recursive --depth 1 "$submodule"
          done

      - uses: actions/setup-node@v4
        with:
          node-version: 20

      - uses: ilammy/msvc-dev-cmd@v1

      - uses: actions/setup-python@v5
        with:
          python-version: "3.10"

      - uses: actions/setup-go@v5
        with:
          go-version: "stable"
          cache: false

      - name: Install tools and dependencies
        run: |
          pip3 install --use-pep517 python-dotenv jinja2
          go install golang.org/dl/go1.24.3@latest && go1.24.3 download
          go env -w GOFLAGS="-buildvcs=false"
          rustup default nightly
          cargo install --force cbindgen

      - name: Get Python executable path
        run: |
          $pythonPath = python -c "import sys; print(sys.executable)"
          Write-Output "Python executable path: $pythonPath"
          $pythonDir = Split-Path $pythonPath
          Write-Output "Python directory path: $pythonDir"
          echo "PYTHON3_PATH=$pythonDir" | Out-File -FilePath $env:GITHUB_ENV -Encoding utf8 -Append
        shell: pwsh

      - name: Use Python path
        run: |
          Write-Output "The Python directory is located at: $env:PYTHON3_PATH"
        shell: pwsh

      - name: Download build artifacts
        uses: actions/download-artifact@v4
        with:
          name: tests-artifacts-${{ matrix.build_type }}
          path: out/win/x64

      - name: Extract tests artifacts preserving permissions
        shell: pwsh
        run: |
          Expand-Archive -Path "out/win/x64/tests-artifacts.zip" -DestinationPath "out/win/x64" -Force

      - name: Install Python dependencies via script
        run: |
          python .github/tools/setup_pytest_dependencies.py

      - name: Run Tests (ten_manager pytest tests)
        run: |
          cd out/win/x64/
          pytest -s tests/ten_manager/

      - name: Run Tests (ten_runtime pytest tests)
        env:
          TEN_ENABLE_BACKTRACE_DUMP: "true"
        run: |
          $ENV:PATH += ";$PWD/core/ten_gn"
          cd out/win/x64/
          pytest -s tests/ten_runtime/integration/
      #   continue-on-error: true
      # - name: Setup tmate session
      #   uses: mxschmitt/action-tmate@v3
